{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    },\n    // 是否显示图表切换器\n    showChartSwitcher: {\n      type: Boolean,\n      default: false\n    },\n    // 是否只显示切换器（不显示图表）\n    switcherOnly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null,\n      // 本地图表配置副本\n      dropdownOpen: false,\n      // 下拉框是否打开\n      resizeObserver: null,\n      // 大小变化监听器\n\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'chart-icon-bar',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'chart-icon-line',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'chart-icon-pie',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'chart-icon-bar-horizontal',\n          compatible: true\n        }\n      }\n    };\n  },\n  computed: {\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n\n      // 将当前类型放在第一位\n      const currentType = this.currentChartType;\n      const otherTypes = allTypes.filter(type => type !== currentType);\n      const orderedTypes = currentType ? [currentType, ...otherTypes] : allTypes;\n      return orderedTypes.map(type => ({\n        type: type,\n        name: this.chartTypeConfig[type]?.name || type,\n        icon: this.chartTypeConfig[type]?.icon || 'chart-icon-bar',\n        compatible: this.chartTypeConfig[type]?.compatible !== false\n      }));\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        // 重新渲染图表\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n    // 添加点击外部关闭下拉框的监听\n    document.addEventListener('click', this.handleClickOutside);\n    // 设置图表容器大小变化监听\n    this.setupResizeObserver();\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 移除点击外部监听\n    document.removeEventListener('click', this.handleClickOutside);\n    // 移除大小变化监听器\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n      console.log(`切换图表类型: ${this.currentChartType} -> ${newType}`);\n      this.switchingChart = true;\n      try {\n        // 创建新的图表配置\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 更新当前图表类型\n        this.currentChartType = newType;\n        this.localChartConfig = newChartConfig;\n\n        // 触发事件通知父组件\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 重新渲染图表\n        await this.$nextTick();\n        this.renderChart();\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n    // 切换下拉框显示状态\n    toggleDropdown() {\n      this.dropdownOpen = !this.dropdownOpen;\n    },\n    // 选择图表类型并关闭下拉框\n    selectChartType(chartType) {\n      this.switchChartType(chartType);\n      this.dropdownOpen = false;\n    },\n    // 获取当前图表类型的图标\n    getCurrentChartIcon() {\n      const currentType = this.availableChartTypes.find(type => type.type === this.currentChartType);\n      return currentType ? currentType.icon : 'chart-icon-bar';\n    },\n    // 处理点击外部关闭下拉框\n    handleClickOutside(event) {\n      const dropdown = this.$el.querySelector('.chart-dropdown-wrapper');\n      if (dropdown && !dropdown.contains(event.target)) {\n        this.dropdownOpen = false;\n      }\n    },\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用配置中的类型\n      const type = this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || '',\n          top: 20,\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        grid: {\n          top: 80,\n          left: 60,\n          right: 40,\n          bottom: 60,\n          containLabel: true\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name),\n          top: 50\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || '',\n          top: 20,\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 80,\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    },\n    // 设置图表容器大小变化监听\n    setupResizeObserver() {\n      this.$nextTick(() => {\n        const chartContainer = this.$refs.chartRef;\n        if (chartContainer && window.ResizeObserver) {\n          this.resizeObserver = new ResizeObserver(() => {\n            // 当容器大小改变时，重新调整图表大小\n            this.$nextTick(() => {\n              this.resizeChart();\n            });\n          });\n          this.resizeObserver.observe(chartContainer);\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "recommendedChartTypes", "Array", "showChartSwitcher", "Boolean", "switcherOnly", "data", "loading", "error", "errorMsg", "chartInstance", "<PERSON><PERSON><PERSON>", "currentChartType", "localChartConfig", "dropdownOpen", "resizeObserver", "chartTypeConfig", "icon", "compatible", "computed", "availableChartTypes", "allTypes", "currentType", "otherTypes", "filter", "orderedTypes", "map", "watch", "deep", "handler", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "document", "handleClickOutside", "setupResizeObserver", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "disconnect", "dispose", "methods", "switchChartType", "newType", "console", "log", "newChartConfig", "$emit", "$nextTick", "$message", "message", "toggleDropdown", "selectChartType", "chartType", "getCurrentChartIcon", "find", "event", "dropdown", "$el", "querySelector", "contains", "target", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "then", "response", "code", "msg", "chartData", "catch", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "f", "groupType", "xAxisData", "item", "field", "series", "length", "categoriesSet", "Set", "for<PERSON>ach", "s", "add", "category", "categories", "from", "seriesData", "value", "push", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "top", "left", "textStyle", "fontSize", "fontWeight", "grid", "right", "bottom", "containLabel", "formatter", "orient", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "labelLine", "yAxisData", "$refs", "chartRef", "init", "setOption", "resize", "chartContainer", "ResizeObserver", "observe"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表切换下拉选择器 -->\n    <div v-if=\"showChartSwitcher\" class=\"chart-switcher-dropdown\">\n      <div class=\"chart-dropdown-wrapper\" @click=\"toggleDropdown\" :class=\"{ 'open': dropdownOpen }\">\n        <!-- 当前选中的图表类型按钮 -->\n        <div class=\"chart-current-btn\">\n          <div class=\"chart-icon-wrapper\">\n            <i :class=\"getCurrentChartIcon()\"></i>\n            <div v-if=\"switchingChart\" class=\"switching-loader\">\n              <i class=\"el-icon-loading\"></i>\n            </div>\n          </div>\n          <span class=\"chart-type-text\">图表切换</span>\n          <i class=\"el-icon-arrow-down dropdown-arrow\"></i>\n        </div>\n\n        <!-- 下拉选项 -->\n        <div v-show=\"dropdownOpen\" class=\"chart-dropdown-options\">\n          <div class=\"chart-type-grid\">\n            <button\n              v-for=\"chartType in availableChartTypes\"\n              :key=\"chartType.type\"\n              :class=\"['chart-type-btn', {\n                'active': currentChartType === chartType.type\n              }]\"\n              :disabled=\"switchingChart\"\n              @click.stop=\"selectChartType(chartType.type)\"\n              :title=\"chartType.name\"\n            >\n              <div class=\"chart-icon-wrapper\">\n                <i :class=\"chartType.icon\"></i>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 图表显示区域 - 只在非switcherOnly模式下显示 -->\n    <div v-if=\"!switcherOnly\" class=\"chart-main\">\n      <div v-if=\"loading\" class=\"chart-loading\">\n        <i class=\"el-icon-loading\"></i>\n        <span>加载图表中...</span>\n      </div>\n      <div v-else-if=\"error\" class=\"chart-error\">\n        <i class=\"el-icon-warning\"></i>\n        <span>{{ errorMsg }}</span>\n      </div>\n      <div v-else class=\"chart-canvas-wrapper\">\n        <div ref=\"chartRef\" class=\"chart-canvas resizable-chart\"></div>\n        <div class=\"resize-hint\">拖拽右下角调整图表大小</div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    },\n    // 是否显示图表切换器\n    showChartSwitcher: {\n      type: Boolean,\n      default: false\n    },\n    // 是否只显示切换器（不显示图表）\n    switcherOnly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null, // 本地图表配置副本\n      dropdownOpen: false, // 下拉框是否打开\n      resizeObserver: null, // 大小变化监听器\n\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'chart-icon-bar',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'chart-icon-line',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'chart-icon-pie',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'chart-icon-bar-horizontal',\n          compatible: true\n        }\n      }\n    }\n  },\n  computed: {\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n\n      // 将当前类型放在第一位\n      const currentType = this.currentChartType;\n      const otherTypes = allTypes.filter(type => type !== currentType);\n      const orderedTypes = currentType ? [currentType, ...otherTypes] : allTypes;\n\n      return orderedTypes.map(type => ({\n        type: type,\n        name: this.chartTypeConfig[type]?.name || type,\n        icon: this.chartTypeConfig[type]?.icon || 'chart-icon-bar',\n        compatible: this.chartTypeConfig[type]?.compatible !== false\n      }));\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        // 重新渲染图表\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n    // 添加点击外部关闭下拉框的监听\n    document.addEventListener('click', this.handleClickOutside);\n    // 设置图表容器大小变化监听\n    this.setupResizeObserver();\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 移除点击外部监听\n    document.removeEventListener('click', this.handleClickOutside);\n    // 移除大小变化监听器\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n\n      console.log(`切换图表类型: ${this.currentChartType} -> ${newType}`);\n\n      this.switchingChart = true;\n\n      try {\n        // 创建新的图表配置\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 更新当前图表类型\n        this.currentChartType = newType;\n        this.localChartConfig = newChartConfig;\n\n        // 触发事件通知父组件\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 重新渲染图表\n        await this.$nextTick();\n        this.renderChart();\n\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n\n    // 切换下拉框显示状态\n    toggleDropdown() {\n      this.dropdownOpen = !this.dropdownOpen;\n    },\n\n    // 选择图表类型并关闭下拉框\n    selectChartType(chartType) {\n      this.switchChartType(chartType);\n      this.dropdownOpen = false;\n    },\n\n    // 获取当前图表类型的图标\n    getCurrentChartIcon() {\n      const currentType = this.availableChartTypes.find(type => type.type === this.currentChartType);\n      return currentType ? currentType.icon : 'chart-icon-bar';\n    },\n\n    // 处理点击外部关闭下拉框\n    handleClickOutside(event) {\n      const dropdown = this.$el.querySelector('.chart-dropdown-wrapper');\n      if (dropdown && !dropdown.contains(event.target)) {\n        this.dropdownOpen = false;\n      }\n    },\n\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n\n    // 将原始数据转换为echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用配置中的类型\n      const type = this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || '',\n          top: 20,\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        grid: {\n          top: 80,\n          left: 60,\n          right: 40,\n          bottom: 60,\n          containLabel: true\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name),\n          top: 50\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || '',\n          top: 20,\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 80,\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    },\n\n    // 设置图表容器大小变化监听\n    setupResizeObserver() {\n      this.$nextTick(() => {\n        const chartContainer = this.$refs.chartRef;\n        if (chartContainer && window.ResizeObserver) {\n          this.resizeObserver = new ResizeObserver(() => {\n            // 当容器大小改变时，重新调整图表大小\n            this.$nextTick(() => {\n              this.resizeChart();\n            });\n          });\n          this.resizeObserver.observe(chartContainer);\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: auto;\n  min-height: 280px; /* 减少高度从350px到280px */\n  position: relative;\n  max-width: 168px; /* 缩短40%：从280px到168px */\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n.chart-wrapper {\n  position: relative;\n  width: 100%;\n  height: 280px; /* 减少高度从350px到280px */\n}\n\n/* 图表容器包装器 */\n.chart-canvas-wrapper {\n  position: relative;\n  /* 不设置固定宽高，让子元素自由调整大小 */\n  display: inline-block;\n  min-width: 300px;\n  min-height: 200px;\n}\n\n/* 可调整大小的图表容器 */\n.chart-canvas {\n  /* 设置固定的初始尺寸，而不是百分比，以支持 resize 功能 */\n  width: 800px;\n  height: 400px;\n  position: relative;\n  z-index: 1;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 添加拖拽调整大小功能 */\n.resizable-chart {\n  resize: both;\n  overflow: hidden;\n  min-width: 300px;\n  min-height: 200px;\n  /* 移除 max-width 限制，允许用户往右拖拽到任意宽度 */\n  max-height: 800px;\n}\n\n/* 鼠标悬停时显示可调整大小的提示 */\n.resizable-chart:hover {\n  border-color: #409eff;\n  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);\n}\n\n/* 调整大小提示文字 */\n.resize-hint {\n  position: absolute;\n  bottom: -20px;\n  right: 0;\n  font-size: 11px;\n  color: #909399;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  pointer-events: none;\n}\n\n.chart-canvas-wrapper:hover .resize-hint {\n  opacity: 1;\n}\n\n.chart-loading, .chart-error, .chart-switch-loading {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i, .chart-switch-loading i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n\n.chart-switch-loading {\n  background: rgba(255,255,255,0.8);\n  backdrop-filter: blur(2px);\n}\n\n/* 图表切换过渡动画 */\n.chart-fade-enter-active, .chart-fade-leave-active {\n  transition: opacity 0.3s ease;\n}\n\n.chart-fade-enter, .chart-fade-leave-to {\n  opacity: 0;\n}\n\n/* 响应式设计 */\n/* 图表切换下拉选择器样式 */\n.chart-switcher-dropdown {\n  position: relative;\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.chart-dropdown-wrapper {\n  position: relative;\n  cursor: pointer;\n}\n\n.chart-current-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  height: 28px;\n  padding: 0 12px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background: #fff;\n  color: #606266;\n  font-size: 12px;\n  line-height: 1;\n  white-space: nowrap;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-sizing: border-box;\n  min-width: 100px;\n}\n\n.chart-current-btn:hover {\n  background: #ecf5ff;\n  border-color: #409eff;\n  color: #409eff;\n}\n\n.chart-dropdown-wrapper.open .chart-current-btn {\n  background: #ecf5ff;\n  border-color: #409eff;\n  color: #409eff;\n}\n\n.chart-type-text {\n  font-size: 12px;\n  color: inherit;\n}\n\n.dropdown-arrow {\n  font-size: 10px;\n  color: inherit;\n  transition: transform 0.3s ease;\n}\n\n.chart-dropdown-wrapper.open .dropdown-arrow {\n  transform: rotate(180deg);\n}\n\n.chart-dropdown-options {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  background: #fff;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  margin-top: 4px;\n  padding: 8px;\n  min-width: 120px;\n}\n\n.chart-type-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.chart-type-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 50px;\n  height: 50px;\n  border: 2px solid #dee2e6;\n  border-radius: 8px;\n  background: #fff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.chart-type-btn:hover {\n  border-color: #409eff;\n  background: #ecf5ff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\n}\n\n.chart-type-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: #fff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n.chart-type-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.chart-icon-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.switching-loader {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.switching-loader i {\n  font-size: 12px;\n  animation: rotating 1s linear infinite;\n}\n\n@keyframes rotating {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 自定义图表图标样式 */\n.chart-icon-bar::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 17h2v-7H7v7zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-line::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-pie::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-bar-horizontal::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z' transform='rotate(90 12 12)'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n@media (max-width: 768px) {\n  .chart-container {\n    max-width: 100%;\n    margin: 0;\n    padding: 0 10px;\n  }\n\n  .chart-wrapper {\n    height: 250px; /* 移动端进一步减少高度 */\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AA0DA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,qBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAF,OAAA,EAAAA,CAAA;IACA;IACA;IACAG,iBAAA;MACAN,IAAA,EAAAO,OAAA;MACAJ,OAAA;IACA;IACA;IACAK,YAAA;MACAR,IAAA,EAAAO,OAAA;MACAJ,OAAA;IACA;EACA;EACAM,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,gBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;;MAEA;MACAC,eAAA;QACA;UACAtB,IAAA;UACAuB,IAAA;UACAC,UAAA;QACA;QACA;UACAxB,IAAA;UACAuB,IAAA;UACAC,UAAA;QACA;QACA;UACAxB,IAAA;UACAuB,IAAA;UACAC,UAAA;QACA;QACA;UACAxB,IAAA;UACAuB,IAAA;UACAC,UAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,oBAAA;MACA,MAAAC,QAAA;;MAEA;MACA,MAAAC,WAAA,QAAAV,gBAAA;MACA,MAAAW,UAAA,GAAAF,QAAA,CAAAG,MAAA,CAAA3B,IAAA,IAAAA,IAAA,KAAAyB,WAAA;MACA,MAAAG,YAAA,GAAAH,WAAA,IAAAA,WAAA,KAAAC,UAAA,IAAAF,QAAA;MAEA,OAAAI,YAAA,CAAAC,GAAA,CAAA7B,IAAA;QACAA,IAAA,EAAAA,IAAA;QACAH,IAAA,OAAAsB,eAAA,CAAAnB,IAAA,GAAAH,IAAA,IAAAG,IAAA;QACAoB,IAAA,OAAAD,eAAA,CAAAnB,IAAA,GAAAoB,IAAA;QACAC,UAAA,OAAAF,eAAA,CAAAnB,IAAA,GAAAqB,UAAA;MACA;IACA;EACA;EACAS,KAAA;IACA/B,WAAA;MACAgC,IAAA;MACAC,QAAA;QACA;QACA,KAAAC,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,SAAAnC,WAAA,SAAAA,WAAA,CAAAC,IAAA;MACA,KAAAe,gBAAA,QAAAhB,WAAA,CAAAC,IAAA;IACA;IACA,KAAAiC,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;IACA;IACAC,QAAA,CAAAF,gBAAA,eAAAG,kBAAA;IACA;IACA,KAAAC,mBAAA;EACA;EACAC,cAAA;IACA;IACAN,MAAA,CAAAO,mBAAA,gBAAAL,WAAA;IACA;IACAC,QAAA,CAAAI,mBAAA,eAAAH,kBAAA;IACA;IACA,SAAArB,cAAA;MACA,KAAAA,cAAA,CAAAyB,UAAA;IACA;IACA;IACA,SAAA9B,aAAA;MACA,KAAAA,aAAA,CAAA+B,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAC,gBAAAC,OAAA;MACA,IAAAA,OAAA,UAAAhC,gBAAA,SAAAD,cAAA;QACA;MACA;MAEAkC,OAAA,CAAAC,GAAA,iBAAAlC,gBAAA,OAAAgC,OAAA;MAEA,KAAAjC,cAAA;MAEA;QACA;QACA,MAAAoC,cAAA;UACA,QAAAnD,WAAA;UACAC,IAAA,EAAA+C;QACA;;QAEA;QACA,KAAAhC,gBAAA,GAAAgC,OAAA;QACA,KAAA/B,gBAAA,GAAAkC,cAAA;;QAEA;QACA,KAAAC,KAAA,uBAAAD,cAAA;;QAEA;QACA,WAAAE,SAAA;QACA,KAAAnB,WAAA;MAEA,SAAAtB,KAAA;QACAqC,OAAA,CAAArC,KAAA,cAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA,gBAAAA,KAAA,CAAA2C,OAAA;MACA;QACA,KAAAxC,cAAA;MACA;IACA;IAEA;IACAyC,eAAA;MACA,KAAAtC,YAAA,SAAAA,YAAA;IACA;IAEA;IACAuC,gBAAAC,SAAA;MACA,KAAAX,eAAA,CAAAW,SAAA;MACA,KAAAxC,YAAA;IACA;IAEA;IACAyC,oBAAA;MACA,MAAAjC,WAAA,QAAAF,mBAAA,CAAAoC,IAAA,CAAA3D,IAAA,IAAAA,IAAA,CAAAA,IAAA,UAAAe,gBAAA;MACA,OAAAU,WAAA,GAAAA,WAAA,CAAAL,IAAA;IACA;IAEA;IACAmB,mBAAAqB,KAAA;MACA,MAAAC,QAAA,QAAAC,GAAA,CAAAC,aAAA;MACA,IAAAF,QAAA,KAAAA,QAAA,CAAAG,QAAA,CAAAJ,KAAA,CAAAK,MAAA;QACA,KAAAhD,YAAA;MACA;IACA;IAEAgB,YAAA;MACA,KAAAvB,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAZ,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAU,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAoC,OAAA,CAAArC,KAAA,iBAAAZ,WAAA;QACA;MACA;MAEAiD,OAAA,CAAAC,GAAA,oBAAAlD,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAAU,IAAA;QACAuC,OAAA,CAAAC,GAAA;QACA,KAAAvC,OAAA;QAEA;UACA;UACA,MAAAwD,OAAA,QAAAC,qBAAA,MAAApE,WAAA;UACAiD,OAAA,CAAAC,GAAA,kBAAAiB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAvD,KAAA;UACAqC,OAAA,CAAArC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;QACA;QACA;MACA;;MAEA;MACA3D,OAAA,MAAAI,WAAA,EAAAsE,IAAA,CAAAC,QAAA;QACAtB,OAAA,CAAAC,GAAA,eAAAqB,QAAA;QACA,KAAA5D,OAAA;QAEA,KAAA4D,QAAA;UACA,KAAA3D,KAAA;UACA,KAAAC,QAAA;UACAoC,OAAA,CAAArC,KAAA;UACA;QACA;QAEA,IAAA2D,QAAA,CAAAC,IAAA;UACA,KAAA5D,KAAA;UACA,KAAAC,QAAA,GAAA0D,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACAvB,OAAA,CAAArC,KAAA,cAAA2D,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAA7D,IAAA;QACAuC,OAAA,CAAAC,GAAA,cAAAwB,SAAA;QAEA;UACA;UACA,MAAAP,OAAA,QAAAC,qBAAA,CAAAM,SAAA;UACAzB,OAAA,CAAAC,GAAA,kBAAAiB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAvD,KAAA;UACAqC,OAAA,CAAArC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;QACA;MACA,GAAAoB,KAAA,CAAA/D,KAAA;QACAqC,OAAA,CAAArC,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACAa,sBAAAM,SAAA;MACAzB,OAAA,CAAAC,GAAA,yBAAAwB,SAAA;;MAEA;MACA,MAAAzE,IAAA,QAAAD,WAAA,CAAAC,IAAA;;MAEA;MACA,IAAAyE,SAAA,CAAAhE,IAAA,IAAAJ,KAAA,CAAAsE,OAAA,CAAAF,SAAA,CAAAhE,IAAA;QACAuC,OAAA,CAAAC,GAAA;QACA;QACA,QAAAjD,IAAA;UACA;YACA,YAAA4E,kBAAA,CAAAH,SAAA;UACA;YACA,YAAAI,mBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,kBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,uBAAA,CAAAN,SAAA;UACA;YACA,YAAAO,iBAAA;QACA;MACA;QACAhC,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAgC,aAAA;;QAEA;QACA,IAAAR,SAAA,CAAAhE,IAAA,IAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA;UACAuC,OAAA,CAAAC,GAAA;UACAgC,aAAA;YACAjF,IAAA,EAAAyE,SAAA,CAAAhE,IAAA,CAAAT,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAS,IAAA,EAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA;YACAyE,OAAA,EAAAT,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,GAAAV,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,CAAAxD,MAAA,CAAAyD,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAJ,aAAA;UACAjC,OAAA,CAAAC,GAAA;UACA,YAAA+B,iBAAA;QACA;QAEAhC,OAAA,CAAAC,GAAA,WAAAgC,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAAjF,IAAA;UACA;YACA,YAAA4E,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAH,SAAA;MACAzB,OAAA,CAAAC,GAAA,gBAAAwB,SAAA;;MAEA;MACA,IAAAhE,IAAA;MACA,IAAAyE,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAAhE,IAAA,IAAAJ,KAAA,CAAAsE,OAAA,CAAAF,SAAA,CAAAhE,IAAA;QACAA,IAAA,GAAAgE,SAAA,CAAAhE,IAAA;QACAyE,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAAhE,IAAA,IAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA,IAAAJ,KAAA,CAAAsE,OAAA,CAAAF,SAAA,CAAAhE,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA;QACAyE,OAAA,GAAAT,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,GACAV,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,CAAAxD,MAAA,CAAAyD,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEArC,OAAA,CAAAC,GAAA,YAAAxC,IAAA;MACAuC,OAAA,CAAAC,GAAA,QAAAiC,OAAA;;MAEA;MACA,MAAAI,SAAA,GAAA7E,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA1F,IAAA;;MAEA;MACA,MAAA4F,MAAA;MACA,IAAAhF,IAAA,CAAAiF,MAAA,QAAAjF,IAAA,IAAAgF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAnF,IAAA,CAAAoF,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA5F,KAAA,CAAA6F,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAAoB,GAAA,CAAA0D,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAA9B,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAmG,QAAA;YACAhG,IAAA;YACAS,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAqF,OAAA,KAAArF,IAAA;UACAG,IAAA;UACAS,IAAA,EAAAA,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxG,WAAA,CAAAuG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1G,IAAA;UACA;QACA;QACA2G,MAAA;UACAlG,IAAA,EAAAgF,MAAA,CAAA5D,GAAA,CAAAiE,CAAA,IAAAA,CAAA,CAAAjG,IAAA;QACA;QACA+G,KAAA;UACA5G,IAAA;UACAS,IAAA,EAAA6E;QACA;QACAuB,KAAA;UACA7G,IAAA;QACA;QACAyF,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,oBAAAJ,SAAA;MACA;QAAAhE,IAAA;QAAAyE,OAAA;MAAA,IAAAT,SAAA;;MAEA;MACA,MAAAa,SAAA,GAAA7E,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAAhF,IAAA,CAAAiF,MAAA,QAAAjF,IAAA,IAAAgF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAnF,IAAA,CAAAoF,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA5F,KAAA,CAAA6F,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAAoB,GAAA,CAAA0D,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAA9B,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAmG,QAAA;YACAhG,IAAA;YACAS,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAqF,OAAA,KAAArF,IAAA;UACAG,IAAA;UACAS,IAAA,EAAAA,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxG,WAAA,CAAAuG,KAAA;UACAQ,GAAA;UACAC,IAAA;UACAC,SAAA;YACAC,QAAA;YACAC,UAAA;UACA;QACA;QACAC,IAAA;UACAL,GAAA;UACAC,IAAA;UACAK,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAd,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAlG,IAAA,EAAAgF,MAAA,CAAA5D,GAAA,CAAAiE,CAAA,IAAAA,CAAA,CAAAjG,IAAA;UACAiH,GAAA;QACA;QACAF,KAAA;UACA5G,IAAA;UACAS,IAAA,EAAA6E;QACA;QACAuB,KAAA;UACA7G,IAAA;QACA;QACAyF,MAAA,EAAAA;MACA;IACA;IAEA;IACAX,mBAAAL,SAAA;MACA;QAAAhE,IAAA;MAAA,IAAAgE,SAAA;MAEA,MAAA0B,UAAA,GAAA1F,IAAA,CAAAoB,GAAA,CAAA0D,IAAA;QACA1F,IAAA,EAAA0F,IAAA,CAAAC,KAAA;QACAY,KAAA,EAAAb,IAAA,CAAAa;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxG,WAAA,CAAAuG,KAAA;UACAQ,GAAA;UACAC,IAAA;UACAC,SAAA;YACAC,QAAA;YACAC,UAAA;UACA;QACA;QACAV,OAAA;UACAC,OAAA;UACAc,SAAA;QACA;QACAZ,MAAA;UACAa,MAAA;UACAJ,KAAA;UACAN,GAAA;UACArG,IAAA,EAAA0F,UAAA,CAAAtE,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAA1F,IAAA;QACA;QACA4F,MAAA;UACA5F,IAAA;UACAG,IAAA;UACAyH,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAX,QAAA;cACAC,UAAA;YACA;UACA;UACAa,SAAA;YACAH,IAAA;UACA;UACAnH,IAAA,EAAA0F;QACA;MACA;IACA;IAEA;IACApB,wBAAAN,SAAA;MACAzB,OAAA,CAAAC,GAAA,gBAAAwB,SAAA;;MAEA;MACA,IAAAhE,IAAA;MACA,IAAAyE,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAAhE,IAAA,IAAAJ,KAAA,CAAAsE,OAAA,CAAAF,SAAA,CAAAhE,IAAA;QACAA,IAAA,GAAAgE,SAAA,CAAAhE,IAAA;QACAyE,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAAhE,IAAA,IAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA,IAAAJ,KAAA,CAAAsE,OAAA,CAAAF,SAAA,CAAAhE,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgE,SAAA,CAAAhE,IAAA,CAAAA,IAAA;QACAyE,OAAA,GAAAT,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,GACAV,SAAA,CAAAhE,IAAA,CAAA0E,MAAA,CAAAxD,MAAA,CAAAyD,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEArC,OAAA,CAAAC,GAAA,YAAAxC,IAAA;MACAuC,OAAA,CAAAC,GAAA,QAAAiC,OAAA;;MAEA;MACA,MAAA8C,SAAA,GAAAvH,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA1F,IAAA;;MAEA;MACA,MAAA4F,MAAA;MACA,IAAAhF,IAAA,CAAAiF,MAAA,QAAAjF,IAAA,IAAAgF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAnF,IAAA,CAAAoF,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA5F,KAAA,CAAA6F,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAAoB,GAAA,CAAA0D,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAA9B,IAAA,CAAAmC,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAmG,QAAA;YACAhG,IAAA;YAAA;YACAS,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAqF,OAAA,KAAArF,IAAA;UACAG,IAAA;UAAA;UACAS,IAAA,EAAAA,IAAA,CAAAoB,GAAA,CAAA0D,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxG,WAAA,CAAAuG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1G,IAAA;UACA;QACA;QACA2G,MAAA;UACAlG,IAAA,EAAAgF,MAAA,CAAA5D,GAAA,CAAAiE,CAAA,IAAAA,CAAA,CAAAjG,IAAA;QACA;QACA;QACA+G,KAAA;UACA5G,IAAA;QACA;QACA6G,KAAA;UACA7G,IAAA;UAAA;UACAS,IAAA,EAAAuH;QACA;QACAvC,MAAA,EAAAA;MACA;IACA;IAEA;IACAT,kBAAA;MACAhC,OAAA,CAAAC,GAAA;MACA;QACAqD,KAAA;UACAC,IAAA,OAAAxG,WAAA,CAAAuG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACA5G,IAAA;UACAS,IAAA;QACA;QACAoG,KAAA;UACA7G,IAAA;QACA;QACAyF,MAAA;UACAzF,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAS,IAAA;QACA;MACA;IACA;IAEA;IACA2D,UAAAF,OAAA;MACA;QACAlB,OAAA,CAAAC,GAAA;QACA,UAAAgF,KAAA,CAAAC,QAAA;UACAlF,OAAA,CAAArC,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAA+B,OAAA;QACA;;QAEA;QACA,KAAA/B,aAAA,GAAAjB,OAAA,CAAAuI,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAArH,aAAA,CAAAuH,SAAA,CAAAlE,OAAA;QACAlB,OAAA,CAAAC,GAAA;MACA,SAAAtC,KAAA;QACAqC,OAAA,CAAArC,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACAjB,YAAA;MACA,SAAAxB,aAAA;QACA,KAAAA,aAAA,CAAAwH,MAAA;MACA;IACA;IAEA;IACA7F,oBAAA;MACA,KAAAY,SAAA;QACA,MAAAkF,cAAA,QAAAL,KAAA,CAAAC,QAAA;QACA,IAAAI,cAAA,IAAAnG,MAAA,CAAAoG,cAAA;UACA,KAAArH,cAAA,OAAAqH,cAAA;YACA;YACA,KAAAnF,SAAA;cACA,KAAAf,WAAA;YACA;UACA;UACA,KAAAnB,cAAA,CAAAsH,OAAA,CAAAF,cAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}