[{"D:\\FastBI\\datafrontPro\\src\\main.js": "1", "D:\\FastBI\\datafrontPro\\src\\App.vue": "2", "D:\\FastBI\\datafrontPro\\src\\components\\ChartDisplay.vue": "3", "D:\\FastBI\\datafrontPro\\src\\api\\index.js": "4"}, {"size": 343, "mtime": 1751352483989, "results": "5", "hashOfConfig": "6"}, {"size": 161405, "mtime": 1753863584210, "results": "7", "hashOfConfig": "6"}, {"size": 29918, "mtime": 1753864917524, "results": "8", "hashOfConfig": "6"}, {"size": 3817, "mtime": 1752743839756, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "12"}, "hk0f3k", {"filePath": "13", "messages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "12"}, "D:\\FastBI\\datafrontPro\\src\\main.js", [], [], "D:\\FastBI\\datafrontPro\\src\\App.vue", [], "D:\\FastBI\\datafrontPro\\src\\components\\ChartDisplay.vue", [], "D:\\FastBI\\datafrontPro\\src\\api\\index.js", []]