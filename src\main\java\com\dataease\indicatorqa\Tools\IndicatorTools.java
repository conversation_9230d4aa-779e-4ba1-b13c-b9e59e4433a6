package com.dataease.indicatorqa.Tools;

import com.dataease.indicatorqa.model.*;
import com.dataease.indicatorqa.service.IndicatorService;
import com.dataease.indicatorqa.util.DataEaseApiClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class IndicatorTools {

    private static final String BASE_URL = "http://192.168.31.119:9535";
    private static final String AUTH_TOKEN ="eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2ZDlhNTg3LTA0MzgtNGMwMC04NmVmLThjNzZmNTVjYjAxYyJ9.-jtbhghdBP37vzFjZ2yWnr5-cvq6DJfGfvjWFM3lZaWo1m5o0NJsriTkcRdV57ALAnn4jhs5TqDQtvOxQcngVw";

    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private DataEaseApiClient apiClient;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CHART_DATA_KEY_PREFIX = "chart_data:";
    private static final long CACHE_EXPIRATION_HOURS = 24; // 缓存24小时后过期

    /**
     * 创建包含认证信息的HTTP头
     */
    private HttpHeaders createAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-DE-TOKEN", AUTH_TOKEN);
        headers.set("out_auth_platform", "default");
        return headers;
    }



    @Tool(name = "getChartData", value = "根据用户需求，调用DataEase API获取图表数据。你需要提供一个符合DataEase /de2api/chartData/getData 接口规范的 `chartType` 和 `parameters` 对象，该`parameters`对象包含 `xAxis`（维度字段列表，`groupType`为'd'）、`yAxis`（指标字段列表，`groupType`为'q'）")
    public String getChartData(@P("图表类型，如'bar', 'line', 'pie', 'table', 'gauge', 'scatter'等") String chartType,
                          @P("图表数据参数配置，JSON对象形式，包含 xAxis, yAxis, customFilter, sortPriority, resultCount等") Map<String, Object> parametersMap) {
        try {
            System.out.println("======= 开始执行getChartData方法 =======");
            System.out.println("接收到的图表类型: " + chartType);
            System.out.println("接收到的参数Map: " + parametersMap);

            // 原有的处理逻辑...
            Map<String, Object> template;
            if ("bar".equalsIgnoreCase(chartType)) {
                template = getBarChartTemplate();
            } else if ("line".equalsIgnoreCase(chartType)) {
                template = getLineChartTemplate();
            } else if ("pie".equalsIgnoreCase(chartType)) {
                template = getPieChartTemplate();
            } else if("bar-horizontal".equalsIgnoreCase(chartType)) {
                template = getBarHorizontalChartTemplate();
            } else {
                template = getBarChartTemplate();
                template.put("type", chartType);
            }
            
            // 合并参数...
            // 2. 处理基础属性
            if (parametersMap.containsKey("id")) {
                template.put("id", parametersMap.get("id"));
            } else {
                template.put("id", generateUniqueId());
            }

            if (parametersMap.containsKey("title")) {
                template.put("title", parametersMap.get("title"));
            } else {
                template.put("title", "生成的" + chartType + "图表");
            }

            if (parametersMap.containsKey("tableId")) {
                template.put("tableId", parametersMap.get("tableId"));
            }

            if (parametersMap.containsKey("resultCount")) {
                template.put("resultCount", parametersMap.get("resultCount"));
            }

            // 3. 处理xAxis字段 - 深度合并
            if (parametersMap.containsKey("xAxis")) {
                List<Map<String, Object>> userXAxis = (List<Map<String, Object>>) parametersMap.get("xAxis");
                List<Map<String, Object>> templateXAxis = new ArrayList<>();

                for (Map<String, Object> field : userXAxis) {
                    Map<String, Object> completeField = new HashMap<>(field);

                    // 设置必要的固定参数
                    completeField.put("chartId", null);
                    completeField.put("dbFieldName", null);
                    completeField.put("description", completeField.getOrDefault("name", ""));
                    completeField.put("precision", null);
                    completeField.put("scale", null);

                    // 设置关键的deType参数（根据groupType）
                    if ("d".equals(completeField.get("groupType"))) {
                        completeField.put("deType", 0);
                        completeField.put("deExtractType", 0);
                    } else {
                        completeField.put("deType", 2);
                        completeField.put("deExtractType", 2);
                    }

                    // 设置其他固定参数
                    completeField.put("extField", completeField.getOrDefault("extField", 0));
                    completeField.put("checked", true);
                    completeField.put("columnIndex", null);
                    completeField.put("lastSyncTime", null);
                    completeField.put("dateFormat", null);
                    completeField.put("dateFormatType", null);

                    // 处理fieldShortName（如果不存在）
                    if (!completeField.containsKey("fieldShortName") && completeField.containsKey("dataeaseName")) {
                        completeField.put("fieldShortName", completeField.get("dataeaseName"));
                    }

                    // 设置默认布尔值
                    completeField.put("desensitized", completeField.getOrDefault("desensitized", false));
                    completeField.put("hide", completeField.getOrDefault("hide", false));
                    completeField.put("agg", completeField.getOrDefault("agg", false));

                    // 确保params存在
                    if (!completeField.containsKey("params")) {
                        completeField.put("params", new ArrayList<>());
                    }

                    // 确保summary和sort有默认值
                    completeField.put("summary", completeField.getOrDefault("summary", "none"));
                    completeField.put("sort", completeField.getOrDefault("sort", "none"));

                    // 设置日期相关参数
                    completeField.put("dateStyle", "y_M_d");
                    completeField.put("datePattern", "date_sub");

                    // 设置图表类型
                    if (!completeField.containsKey("chartType")) {
                        completeField.put("chartType", chartType);
                    }

                    // 确保compareCalc存在
                    if (!completeField.containsKey("compareCalc")) {
                        Map<String, Object> compareCalc = new HashMap<>();
                        compareCalc.put("type", "none");
                        compareCalc.put("resultData", "percent");
                        compareCalc.put("field", null);
                        compareCalc.put("custom", null);
                        completeField.put("compareCalc", compareCalc);
                    }

                    // 设置其他null值参数
                    completeField.put("logic", null);
                    completeField.put("filterType", null);
                    completeField.put("index", null);

                    // 确保formatterCfg存在
                    if (!completeField.containsKey("formatterCfg")) {
                        Map<String, Object> formatterCfg = new HashMap<>();
                        formatterCfg.put("type", "auto");
                        formatterCfg.put("unit", 1);
                        formatterCfg.put("suffix", "");
                        formatterCfg.put("decimalCount", 2);
                        formatterCfg.put("thousandSeparator", true);
                        completeField.put("formatterCfg", formatterCfg);
                    }

                    completeField.put("chartShowName", null);

                    // 确保filter存在
                    if (!completeField.containsKey("filter")) {
                        completeField.put("filter", new ArrayList<>());
                    }

                    completeField.put("customSort", null);
                    completeField.put("busiType", null);

                    templateXAxis.add(completeField);
                }

                template.put("xAxis", templateXAxis);
            }

            // 4. 处理yAxis字段 - 深度合并
            if (parametersMap.containsKey("yAxis")) {
                List<Map<String, Object>> userYAxis = (List<Map<String, Object>>) parametersMap.get("yAxis");
                List<Map<String, Object>> templateYAxis = new ArrayList<>();

                for (Map<String, Object> field : userYAxis) {
                    Map<String, Object> completeField = new HashMap<>(field);

                    // 设置必要的固定参数
                    completeField.put("datasourceId", completeField.getOrDefault("datasourceId", null));
                    completeField.put("datasetTableId", completeField.getOrDefault("datasetTableId", null));
                    completeField.put("chartId", null);
                    completeField.put("dbFieldName", null);
                    completeField.put("description", completeField.getOrDefault("description", null));
                    completeField.put("precision", null);
                    completeField.put("scale", null);

                    // 设置关键的deType参数
                    completeField.put("deType", 2); // 度量字段的deType通常为2
                    completeField.put("deExtractType", null);

                    // 特殊处理记录数*字段
                    if ("-1".equals(completeField.get("id"))) {
                        completeField.put("columnIndex", 999);
                        completeField.put("fieldShortName", null);
                        completeField.put("desensitized", null);
                        completeField.put("params", null);
                    } else {
                        completeField.put("columnIndex", null);
                        // 处理fieldShortName（如果不存在）
                        if (!completeField.containsKey("fieldShortName") && completeField.containsKey("dataeaseName")) {
                            completeField.put("fieldShortName", completeField.get("dataeaseName"));
                        }
                        completeField.put("desensitized", completeField.getOrDefault("desensitized", false));
                        completeField.put("params", completeField.getOrDefault("params", new ArrayList<>()));
                    }

                    completeField.put("checked", true);
                    completeField.put("lastSyncTime", null);
                    completeField.put("dateFormat", null);
                    completeField.put("dateFormatType", null);

                    // 确保summary和sort有默认值
                    completeField.put("summary", completeField.getOrDefault("summary", "count"));
                    completeField.put("sort", completeField.getOrDefault("sort", "none"));

                    // 设置日期相关参数
                    completeField.put("dateStyle", "y_M_d");
                    completeField.put("datePattern", "date_sub");

                    // 设置图表类型
                    if (!completeField.containsKey("chartType")) {
                        completeField.put("chartType", chartType);
                    }

                    // 确保compareCalc存在
                    if (!completeField.containsKey("compareCalc")) {
                        Map<String, Object> compareCalc = new HashMap<>();
                        compareCalc.put("type", "none");
                        compareCalc.put("resultData", "percent");
                        compareCalc.put("field", null);
                        compareCalc.put("custom", null);
                        completeField.put("compareCalc", compareCalc);
                    }

                    // 设置其他null值参数
                    completeField.put("logic", null);
                    completeField.put("filterType", null);
                    completeField.put("index", null);

                    // 确保formatterCfg存在
                    if (!completeField.containsKey("formatterCfg")) {
                        Map<String, Object> formatterCfg = new HashMap<>();
                        formatterCfg.put("type", "auto");
                        formatterCfg.put("unit", 1);
                        formatterCfg.put("suffix", "");
                        formatterCfg.put("decimalCount", 2);
                        formatterCfg.put("thousandSeparator", true);
                        completeField.put("formatterCfg", formatterCfg);
                    }

                    completeField.put("chartShowName", null);

                    // 确保filter存在
                    if (!completeField.containsKey("filter")) {
                        completeField.put("filter", new ArrayList<>());
                    }

                    completeField.put("customSort", null);
                    completeField.put("busiType", null);
                    completeField.put("hide", completeField.getOrDefault("hide", false));
                    completeField.put("agg", completeField.getOrDefault("agg", false));

                    // 添加Y轴特有字段
                    completeField.put("axisType", "yAxis");
                    String seriesId = completeField.get("id") + "-yAxis";
                    completeField.put("seriesId", seriesId);

                    templateYAxis.add(completeField);
                }

                template.put("yAxis", templateYAxis);
            }

            // 5. 确保customFilter存在
            if (parametersMap.containsKey("customFilter")) {
                template.put("customFilter", parametersMap.get("customFilter"));
            } else {
                template.put("customFilter", new HashMap<>());
            }

            // 6. 确保sortPriority存在
            if (parametersMap.containsKey("sortPriority")) {
                template.put("sortPriority", parametersMap.get("sortPriority"));
            } else {
                template.put("sortPriority", new ArrayList<>());
            }

            // 7. 设置HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-DE-TOKEN", AUTH_TOKEN);
            headers.set("out_auth_platform", "default");

            // 参数合并完成后，补齐所有V2标准必需字段（即使为空也要传）
            template.putIfAbsent("xAxisExt", new ArrayList<>());
            template.putIfAbsent("yAxisExt", new ArrayList<>());
            template.putIfAbsent("extStack", new ArrayList<>());
            template.putIfAbsent("drillFields", new ArrayList<>());
            template.putIfAbsent("viewFields", new ArrayList<>());
            template.putIfAbsent("extBubble", new ArrayList<>());
            template.putIfAbsent("extLabel", new ArrayList<>());
            template.putIfAbsent("extTooltip", new ArrayList<>());
            template.putIfAbsent("flowMapStartName", new ArrayList<>());
            template.putIfAbsent("flowMapEndName", new ArrayList<>());
            template.putIfAbsent("calParams", new ArrayList<>());
            template.putIfAbsent("isPlugin", false);
            template.putIfAbsent("dataFrom", "calc");
            template.putIfAbsent("chartExtRequest", new HashMap<>());

// 然后再创建HttpEntity并发起请求
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(template, headers);

            // 9. 打印发送的请求
            try {
                System.out.println("发起请求前的参数<" + objectMapper.writeValueAsString(template) + "," + headers.toString() + ">");
            } catch (Exception e) {
                System.err.println("无法序列化请求体: " + e.getMessage());
            }

            // 10. 执行请求
            String url = BASE_URL + "/de2api/chartData/getData";
            System.out.println("请求URL: " + url);

            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    request,
                    Map.class);

            // 11. 处理响应
            System.out.println("响应状态: " + response.getStatusCode());
            System.out.println("得到请求后响应数据: " + response.getBody());

            // 将响应数据存储到Redis
            String cacheId = storeChartDataToRedis(response.getBody());
            System.out.println("图表数据已成功获取！<chart_data_id>" + cacheId + "</chart_data_id>");
            
            // 返回包含缓存ID的字符串，使用特殊格式便于前端解析
            return "图表数据已成功获取！<chart_data_id>" + cacheId + "</chart_data_id>";
            
        } catch (Exception e) {
            e.printStackTrace();
            return "获取图表数据失败: " + e.getMessage();
        }
    }

    /**
     * 生成唯一ID
     */
    private String generateUniqueIdS() {
        return "chart_" + System.currentTimeMillis();
    }

    /**
     * 获取默认的画布样式配置
     */
    private String getDefaultCanvasStyleData() {
        // 这里是默认的画布样式JSON字符串，可以从示例中提取
        return "{\"width\":1920,\"height\":1080,\"refreshBrowserEnable\":false,\"refreshBrowserUnit\":\"minute\",\"refreshBrowserTime\":5,\"refreshViewEnable\":false,\"refreshViewLoading\":true,\"refreshUnit\":\"minute\",\"refreshTime\":5,\"popupAvailable\":true,\"popupButtonAvailable\":true,\"suspensionButtonAvailable\":false,\"screenAdaptor\":\"widthFirst\",\"scale\":60,\"scaleWidth\":60,\"scaleHeight\":60,\"backgroundColorSelect\":true,\"backgroundImageEnable\":false,\"backgroundType\":\"backgroundColor\",\"background\":\"\",\"openCommonStyle\":true,\"opacity\":1,\"fontSize\":14,\"themeId\":\"10002\",\"color\":\"#fff\",\"backgroundColor\":\"rgba(2, 4, 8, 1)\",\"dashboard\":{\"gap\":\"yes\",\"gapSize\":5,\"resultMode\":\"all\",\"resultCount\":1000,\"themeColor\":\"dark\",\"mobileSetting\":{\"customSetting\":false,\"imageUrl\":null,\"backgroundType\":\"image\",\"color\":\"#fff\"}},\"component\":{\"chartTitle\":{\"show\":true,\"fontSize\":16,\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"fontFamily\":\"\",\"letterSpace\":\"0\",\"fontShadow\":false,\"color\":\"#FFFFFF\",\"remarkBackgroundColor\":\"#5A5C62\"},\"chartColor\":{\"basicStyle\":{\"colorScheme\":\"default\",\"colors\":[\"#1E90FF\",\"#90EE90\",\"#00CED1\",\"#E2BD84\",\"#7A90E0\",\"#3BA272\",\"#2BE7FF\",\"#0A8ADA\",\"#FFD700\"],\"alpha\":100,\"gradient\":false,\"mapStyle\":\"darkblue\",\"areaBaseColor\":\"#5470C6\",\"areaBorderColor\":\"#EBEEF5\",\"gaugeStyle\":\"default\",\"tableBorderColor\":\"#CCCCCC\",\"tableScrollBarColor\":\"rgba(255, 255, 255, 0.5)\",\"zoomButtonColor\":\"#fff\",\"zoomBackground\":\"#000\"},\"misc\":{\"flowMapConfig\":{\"lineConfig\":{\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\"}},\"nameFontColor\":\"#ffffff\",\"valueFontColor\":\"#5470c6\"},\"tableHeader\":{\"tableHeaderBgColor\":\"#1E90FF\",\"tableHeaderFontColor\":\"#FFFFFF\"},\"tableCell\":{\"tableItemBgColor\":\"#131E42\",\"tableFontColor\":\"#ffffff\",\"tableItemSubBgColor\":\"#EEEEEE\"}},\"chartCommonStyle\":{\"backgroundColorSelect\":false,\"backgroundImageEnable\":false,\"backgroundType\":\"innerImage\",\"innerImage\":\"board/board_1.svg\",\"outerImage\":null,\"innerPadding\":12,\"borderRadius\":0,\"backgroundColor\":\"#131E42\",\"innerImageColor\":\"#1094E5\"},\"filterStyle\":{\"layout\":\"horizontal\",\"titleLayout\":\"left\",\"labelColor\":\"#ffffff\",\"titleColor\":\"#ffffff\",\"color\":\"#FFFFFF\",\"borderColor\":\"#484747\",\"text\":\"#AFAFAF\",\"bgColor\":\"#131C42\"},\"tabStyle\":{\"headPosition\":\"left\",\"headFontColor\":\"#ffffff\",\"headFontActiveColor\":\"#ffffff\",\"headBorderColor\":\"#000000\",\"headBorderActiveColor\":\"#000000\"},\"seniorStyleSetting\":{\"linkageIconColor\":\"#ffffff\",\"drillLayerColor\":\"#ffffff\",\"pagerColor\":\"#ffffff\"}}}";
    }

    /**
     * 生成组件配置数据
     *
     * @param chartId 图表ID
     * @param title 图表标题
     * @param chartType 图表类型
     * @return 组件配置JSON字符串
     */
    private String getDefaultComponentData(String chartId, String title, String chartType) {
        String componentTemplate = "[{\"animations\":[],\"canvasId\":\"canvas-main\",\"events\":{\"checked\":false,\"showTips\":false,\"type\":\"jump\",\"typeList\":[{\"key\":\"jump\",\"label\":\"跳转\"},{\"key\":\"download\",\"label\":\"下载\"},{\"key\":\"share\",\"label\":\"分享\"},{\"key\":\"fullScreen\",\"label\":\"全屏\"},{\"key\":\"showHidden\",\"label\":\"弹窗区域\"},{\"key\":\"refreshDataV\",\"label\":\"刷新\"},{\"key\":\"refreshView\",\"label\":\"刷新图表\"}],\"jump\":{\"value\":\"https://\",\"type\":\"_blank\"},\"download\":{\"value\":true},\"share\":{\"value\":true},\"showHidden\":{\"value\":true},\"refreshDataV\":{\"value\":true},\"refreshView\":{\"value\":true,\"target\":\"all\"}},\"carousel\":{\"enable\":false,\"time\":10},\"multiDimensional\":{\"enable\":false,\"x\":0,\"y\":0,\"z\":0},\"groupStyle\":{},\"isLock\":false,\"maintainRadio\":false,\"aspectRatio\":1,\"isShow\":true,\"category\":\"base\",\"dragging\":false,\"resizing\":false,\"collapseName\":[\"position\",\"background\",\"style\",\"picture\",\"frameLinks\",\"videoLinks\",\"streamLinks\",\"carouselInfo\",\"events\"],\"linkage\":{\"duration\":0,\"data\":[{\"id\":\"\",\"label\":\"\",\"event\":\"\",\"style\":[{\"key\":\"\",\"value\":\"\"}]}]},\"component\":\"UserView\",\"name\":\"CHART_TITLE\",\"label\":\"CHART_TITLE\",\"propValue\":{\"textValue\":\"\",\"urlList\":[]},\"icon\":\"CHART_TYPE\",\"innerType\":\"CHART_TYPE\",\"editing\":false,\"canvasActive\":false,\"actionSelection\":{\"linkageActive\":\"custom\"},\"x\":1,\"y\":1,\"sizeX\":36,\"sizeY\":14,\"style\":{\"rotate\":0,\"opacity\":1,\"borderActive\":false,\"borderWidth\":1,\"borderRadius\":5,\"borderStyle\":\"solid\",\"borderColor\":\"#CCCCCC\",\"adaptation\":\"adaptation\",\"width\":360,\"height\":180,\"top\":234,\"left\":396},\"matrixStyle\":{},\"commonBackground\":{\"backgroundColorSelect\":false,\"backgroundImageEnable\":false,\"backgroundType\":\"innerImage\",\"innerImage\":\"board/board_1.svg\",\"outerImage\":null,\"innerPadding\":12,\"borderRadius\":0,\"backgroundColor\":\"#131E42\",\"innerImageColor\":\"#1094E5\"},\"state\":\"ready\",\"render\":\"antv\",\"isPlugin\":false,\"id\":\"CHART_ID\",\"linkageFilters\":[]}]";

        return componentTemplate
                .replace("CHART_TITLE", title)
                .replace("CHART_TYPE", chartType)
                .replace("CHART_ID", chartId);
    }

    /**
     * 生成并保存图表到数据大屏 - 高级整合方法
     *
     * @param query 用户查询描述
     * @return 生成的图表信息或错误信息
     */
//    @Tool(name = "生成并保存图表", value = "根据用户描述生成并保存图表到数据大屏")
//    public Map<String, Object> generateAndSaveChart(@P("用户查询描述") String query) {
//        try {
//            // 1. 解析用户查询，生成图表名称
//            String chartName = generateChartName(query);
//
//            // 2. 检查名称是否可用
//            boolean nameValid = checkVisualizationName("add", "leaf", chartName, "dataV", null);
//            if (!nameValid) {
//                // 如果名称不可用，添加时间戳
//                chartName = chartName + "_" + System.currentTimeMillis();
//            }
//
//            // 3. 基于用户查询生成图表参数
//            Map<String, Object> parametersMap = generateChartParameters(query);
//
//            // 4. 获取图表数据
////            Map<String, Object> chartData = getChartData("bar", parametersMap);
//            if (chartData.containsKey("error")) {
//                return Map.of("error", chartData.get("error"));
//            }
//
//            // 5. 保存图表
//            String dataVId = saveCanvas(chartData, chartName);
//
//            if (dataVId != null) {
//                return Map.of(
//                        "success", true,
//                        "message", "图表创建成功: " + chartName,
//                        "chartId", dataVId
//                );
//            } else {
//                return Map.of("error", "保存图表失败");
//            }
//        } catch (Exception e) {
//            return Map.of("error", "生成图表过程中出错: " + e.getMessage());
//        }
//    }

    /**
     * 根据用户查询生成图表名称
     */
    private String generateChartName(String query) {
        // 简单实现，可以使用AI模型来生成更智能的名称
        String name = query
                .replaceAll("[?？!！.。,，:：]", "")
                .trim();

        // 如果名称太长，截取前20个字符
        if (name.length() > 20) {
            name = name.substring(0, 20);
        }

        return name + "图表";
    }

    /**
     * 根据用户查询生成图表参数
     */
    private Map<String, Object> generateChartParameters(String query) {
        // 这里应该使用AI模型分析用户查询，提取指标、维度等
        // 为简单起见，返回一个示例配置
        Map<String, Object> params = new HashMap<>();
        params.put("id", generateUniqueId());
        params.put("title", generateChartName(query));
        params.put("tableId", "1114644377738809344"); // 这应该根据上下文动态获取
        params.put("resultCount", 1000);

        // 具体的xAxis、yAxis等应该根据用户查询智能生成
        // 这里只是示例

        return params;
    }

    /**
     * 生成唯一ID
     */
    private String generateUniqueId() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 获取柱状图模板，包含所有固定参数
     */
    private Map<String, Object> getBarChartTemplate() {
        Map<String, Object> template = new HashMap<>();

        // 基本信息（部分动态，但提供默认值）
        template.put("sceneId", 0);
        template.put("type", "bar");
        template.put("render", "antv");
        template.put("resultMode", "custom");
        template.put("refreshViewEnable", false);
        template.put("refreshTime", 5);
        template.put("refreshUnit", "minute");

        // 占位符（将被动态参数覆盖）
        template.put("xAxis", new ArrayList<>());
        template.put("yAxis", new ArrayList<>());
        template.put("xAxisExt", new ArrayList<>());
        template.put("yAxisExt", new ArrayList<>());
        template.put("customFilter", new HashMap<>());

        // 图表固定参数 - basicStyle
        Map<String, Object> basicStyle = new HashMap<>();
        basicStyle.put("alpha", 100);
        basicStyle.put("tableBorderColor", "#CCCCCC");
        basicStyle.put("tableScrollBarColor", "rgba(255, 255, 255, 0.5)");
        basicStyle.put("tableColumnMode", "adapt");
        basicStyle.put("tableColumnWidth", 100);
        basicStyle.put("tableFieldWidth", new ArrayList<>());
        basicStyle.put("tablePageMode", "page");
        basicStyle.put("tablePageStyle", "simple");
        basicStyle.put("tablePageSize", 20);
        basicStyle.put("gaugeStyle", "default");
        basicStyle.put("colorScheme", "default");
        basicStyle.put("colors", List.of("#1E90FF", "#90EE90", "#00CED1", "#E2BD84", "#7A90E0", "#3BA272", "#2BE7FF", "#0A8ADA", "#FFD700"));
        basicStyle.put("mapVendor", "amap");
        basicStyle.put("gradient", false);
        basicStyle.put("lineWidth", 2);
        basicStyle.put("lineSymbol", "circle");
        basicStyle.put("lineSymbolSize", 4);
        basicStyle.put("lineSmooth", true);
        basicStyle.put("barDefault", true);  // 柱状图特有
        basicStyle.put("radiusColumnBar", "rightAngle");  // 柱状图特有
        basicStyle.put("columnBarRightAngleRadius", 20);  // 柱状图特有
        basicStyle.put("barWidth", 40);  // 柱状图特有
        basicStyle.put("barGap", 0.4);  // 柱状图特有
        basicStyle.put("lineType", "solid");
        basicStyle.put("scatterSymbol", "circle");
        basicStyle.put("scatterSymbolSize", 8);
        basicStyle.put("radarShape", "polygon");
        basicStyle.put("mapStyle", "darkblue");
        basicStyle.put("heatMapType", "heatmap");
        basicStyle.put("heatMapIntensity", 2);
        basicStyle.put("heatMapRadius", 20);
        basicStyle.put("areaBorderColor", "#EBEEF5");
        basicStyle.put("areaBaseColor", "#5470C6");
        basicStyle.put("mapSymbolOpacity", 0.7);
        basicStyle.put("mapSymbolStrokeWidth", 2);
        basicStyle.put("mapSymbol", "circle");
        basicStyle.put("mapSymbolSize", 6);
        basicStyle.put("radius", 80);
        basicStyle.put("innerRadius", 60);
        basicStyle.put("showZoom", true);
        basicStyle.put("zoomButtonColor", "#fff");
        basicStyle.put("zoomBackground", "#000");
        basicStyle.put("tableLayoutMode", "grid");
        basicStyle.put("calcTopN", false);
        basicStyle.put("topN", 5);
        basicStyle.put("topNLabel", "其他");
        basicStyle.put("gaugeAxisLine", true);
        basicStyle.put("gaugePercentLabel", true);
        basicStyle.put("showSummary", false);
        basicStyle.put("summaryLabel", "总计");
        basicStyle.put("seriesColor", new ArrayList<>());
        basicStyle.put("layout", "horizontal");
        basicStyle.put("mapSymbolSizeMin", 4);
        basicStyle.put("mapSymbolSizeMax", 30);
        basicStyle.put("showLabel", true);
        basicStyle.put("mapStyleUrl", "");
        basicStyle.put("autoFit", true);
        Map<String, Object> mapCenter = new HashMap<>();
        mapCenter.put("longitude", 117.232);
        mapCenter.put("latitude", 39.354);
        basicStyle.put("mapCenter", mapCenter);
        basicStyle.put("zoomLevel", 7);
        basicStyle.put("customIcon", "");
        basicStyle.put("showHoverStyle", true);

        // 图表固定参数 - misc
        Map<String, Object> misc = new HashMap<>();
        misc.put("pieInnerRadius", 0);
        misc.put("pieOuterRadius", 80);
        misc.put("radarShape", "polygon");
        misc.put("radarSize", 80);
        misc.put("gaugeMinType", "fix");
        Map<String, Object> gaugeMinField = new HashMap<>();
        gaugeMinField.put("id", "");
        gaugeMinField.put("summary", "");
        misc.put("gaugeMinField", gaugeMinField);
        misc.put("gaugeMin", 0);
        misc.put("gaugeMaxType", "dynamic");
        Map<String, Object> gaugeMaxField = new HashMap<>();
        gaugeMaxField.put("id", "");
        gaugeMaxField.put("summary", "");
        misc.put("gaugeMaxField", gaugeMaxField);
        misc.put("gaugeStartAngle", 225);
        misc.put("gaugeEndAngle", -45);
        misc.put("nameFontSize", 18);
        misc.put("valueFontSize", 18);
        misc.put("nameValueSpace", 10);
        misc.put("valueFontColor", "#5470c6");
        misc.put("valueFontFamily", "Microsoft YaHei");
        misc.put("valueFontIsBolder", false);
        misc.put("valueFontIsItalic", false);
        misc.put("valueLetterSpace", 0);
        misc.put("valueFontShadow", false);
        misc.put("showName", true);
        misc.put("nameFontColor", "#ffffff");
        misc.put("nameFontFamily", "Microsoft YaHei");
        misc.put("nameFontIsBolder", false);
        misc.put("nameFontIsItalic", false);
        misc.put("nameLetterSpace", "0");
        misc.put("nameFontShadow", false);
        misc.put("treemapWidth", 80);
        misc.put("treemapHeight", 80);
        misc.put("liquidMaxType", "dynamic");
        Map<String, Object> liquidMaxField = new HashMap<>();
        liquidMaxField.put("id", "");
        liquidMaxField.put("summary", "");
        misc.put("liquidMaxField", liquidMaxField);
        misc.put("liquidSize", 80);
        misc.put("liquidShape", "circle");
        misc.put("hPosition", "center");
        misc.put("vPosition", "center");
        misc.put("mapPitch", 0);
        misc.put("wordSizeRange", List.of(8, 32));
        misc.put("wordSpacing", 6);
        misc.put("mapAutoLegend", true);
        misc.put("mapLegendMax", 0);
        misc.put("mapLegendMin", 0);
        misc.put("mapLegendNumber", 9);
        misc.put("mapLegendRangeType", "quantize");
        misc.put("mapLegendCustomRange", new ArrayList<>());

        // flowMapConfig
        Map<String, Object> flowMapConfig = new HashMap<>();
        Map<String, Object> lineConfig = new HashMap<>();
        lineConfig.put("mapLineAnimate", true);
        lineConfig.put("mapLineType", "arc");
        lineConfig.put("mapLineWidth", 1);
        lineConfig.put("mapLineAnimateDuration", 3);
        lineConfig.put("mapLineGradient", false);
        lineConfig.put("mapLineSourceColor", "#146C94");
        lineConfig.put("mapLineTargetColor", "#576CBC");
        lineConfig.put("alpha", 100);
        flowMapConfig.put("lineConfig", lineConfig);

        Map<String, Object> pointConfig = new HashMap<>();
        Map<String, Object> textConfig = new HashMap<>();
        textConfig.put("color", "#146C94");
        textConfig.put("fontSize", 10);
        pointConfig.put("text", textConfig);

        Map<String, Object> pointSubConfig = new HashMap<>();
        pointSubConfig.put("color", "#146C94");
        pointSubConfig.put("size", 4);
        pointSubConfig.put("animate", false);
        pointSubConfig.put("speed", 0.01);
        pointConfig.put("point", pointSubConfig);
        flowMapConfig.put("pointConfig", pointConfig);
        misc.put("flowMapConfig", flowMapConfig);

        Map<String, Object> wordCloudAxisValueRange = new HashMap<>();
        wordCloudAxisValueRange.put("auto", true);
        wordCloudAxisValueRange.put("min", 0);
        wordCloudAxisValueRange.put("max", 0);
        misc.put("wordCloudAxisValueRange", wordCloudAxisValueRange);

        // 图表固定参数 - label
        Map<String, Object> label = new HashMap<>();
        label.put("show", false);
        label.put("childrenShow", true);
        label.put("position", "top");
        label.put("color", "#FFFFFF");
        label.put("fontSize", 12);
        label.put("formatter", "");
        Map<String, Object> labelLine = new HashMap<>();
        labelLine.put("show", true);
        label.put("labelLine", labelLine);

        Map<String, Object> labelFormatter = new HashMap<>();
        labelFormatter.put("type", "auto");
        labelFormatter.put("unit", 1);
        labelFormatter.put("suffix", "");
        labelFormatter.put("decimalCount", 2);
        labelFormatter.put("thousandSeparator", true);
        label.put("labelFormatter", labelFormatter);

        label.put("reserveDecimalCount", 2);
        label.put("labelShadow", false);
        label.put("labelBgColor", "");
        label.put("labelShadowColor", "");

        Map<String, Object> quotaLabelFormatter = new HashMap<>();
        quotaLabelFormatter.put("type", "auto");
        quotaLabelFormatter.put("unit", 1);
        quotaLabelFormatter.put("suffix", "");
        quotaLabelFormatter.put("decimalCount", 2);
        quotaLabelFormatter.put("thousandSeparator", true);
        label.put("quotaLabelFormatter", quotaLabelFormatter);

        label.put("showDimension", true);
        label.put("showQuota", false);
        label.put("showProportion", true);
        label.put("seriesLabelFormatter", new ArrayList<>());

        Map<String, Object> conversionTag = new HashMap<>();
        conversionTag.put("show", false);
        conversionTag.put("precision", 2);
        conversionTag.put("text", "转化率");
        label.put("conversionTag", conversionTag);

        label.put("showTotal", false);
        label.put("totalFontSize", 12);
        label.put("totalColor", "#FFF");

        Map<String, Object> totalFormatter = new HashMap<>();
        totalFormatter.put("type", "auto");
        totalFormatter.put("unit", 1);
        totalFormatter.put("suffix", "");
        totalFormatter.put("decimalCount", 2);
        totalFormatter.put("thousandSeparator", true);
        label.put("totalFormatter", totalFormatter);

        label.put("showStackQuota", false);
        label.put("fullDisplay", false);
        label.put("showFields", new ArrayList<>());

        // 图表固定参数 - tooltip
        Map<String, Object> tooltip = new HashMap<>();
        tooltip.put("show", true);
        tooltip.put("trigger", "item");
        tooltip.put("confine", true);
        tooltip.put("fontSize", 12);
        tooltip.put("color", "#FFFFFF");

        Map<String, Object> tooltipFormatter = new HashMap<>();
        tooltipFormatter.put("type", "auto");
        tooltipFormatter.put("unit", 1);
        tooltipFormatter.put("suffix", "");
        tooltipFormatter.put("decimalCount", 2);
        tooltipFormatter.put("thousandSeparator", true);
        tooltip.put("tooltipFormatter", tooltipFormatter);

        tooltip.put("backgroundColor", "#5a5c62");
        tooltip.put("seriesTooltipFormatter", new ArrayList<>());

        Map<String, Object> carousel = new HashMap<>();
        carousel.put("enable", false);
        carousel.put("stayTime", 3);
        carousel.put("intervalTime", 0);
        tooltip.put("carousel", carousel);

        tooltip.put("showFields", new ArrayList<>());

        // 其他图表固定参数 (tableTotal, tableHeader, tableCell等)
        Map<String, Object> tableTotal = new HashMap<>();
        // ...这里省略tableTotal的详细设置，可以按需添加

        // 聚合customAttr
        Map<String, Object> customAttr = new HashMap<>();
        customAttr.put("basicStyle", basicStyle);
        customAttr.put("misc", misc);
        customAttr.put("label", label);
        customAttr.put("tooltip", tooltip);
        customAttr.put("tableTotal", tableTotal);
        // ...添加其他customAttr子对象
        template.put("customAttr", customAttr);

        // 添加customStyle部分
        Map<String, Object> customStyle = new HashMap<>();
        // ...这里省略customStyle的详细设置，可以按需添加

        Map<String, Object> text = new HashMap<>();
        text.put("show", true);
        text.put("fontSize", 16);
        text.put("hPosition", "left");
        text.put("vPosition", "top");
        text.put("isItalic", false);
        text.put("isBolder", true);
        text.put("remarkShow", false);
        text.put("remark", "");
        text.put("fontFamily", "PingFang");
        text.put("letterSpace", "0");
        text.put("fontShadow", false);
        text.put("color", "#FFFFFF");
        text.put("remarkBackgroundColor", "#5A5C62");
        customStyle.put("text", text);

        Map<String, Object> legend = new HashMap<>();
        legend.put("show", true);
        legend.put("hPosition", "center");
        legend.put("vPosition", "bottom");
        legend.put("orient", "horizontal");
        legend.put("icon", "circle");
        legend.put("color", "#FFFFFF");
        legend.put("fontSize", 12);
        customStyle.put("legend", legend);

        Map<String, Object> xAxis = new HashMap<>();
        xAxis.put("show", true);
        xAxis.put("position", "bottom");
        xAxis.put("name", "");
        xAxis.put("color", "#FFFFFF");
        xAxis.put("fontSize", 12);

        Map<String, Object> axisLabel = new HashMap<>();
        axisLabel.put("show", true);
        axisLabel.put("color", "#FFFFFF");
        axisLabel.put("fontSize", 12);
        axisLabel.put("rotate", 0);
        axisLabel.put("formatter", "{value}");
        xAxis.put("axisLabel", axisLabel);

        Map<String, Object> axisLine = new HashMap<>();
        axisLine.put("show", true);
        Map<String, Object> lineStyle = new HashMap<>();
        lineStyle.put("color", "#cccccc");
        lineStyle.put("width", 1);
        lineStyle.put("style", "solid");
        axisLine.put("lineStyle", lineStyle);
        xAxis.put("axisLine", axisLine);

        Map<String, Object> splitLine = new HashMap<>();
        splitLine.put("show", false);
        Map<String, Object> splitLineStyle = new HashMap<>();
        splitLineStyle.put("color", "#858383");
        splitLineStyle.put("width", 1);
        splitLineStyle.put("style", "solid");
        splitLine.put("lineStyle", splitLineStyle);
        xAxis.put("splitLine", splitLine);

        Map<String, Object> axisValue = new HashMap<>();
        axisValue.put("auto", true);
        axisValue.put("min", 10);
        axisValue.put("max", 100);
        axisValue.put("split", 10);
        axisValue.put("splitCount", 10);
        xAxis.put("axisValue", axisValue);

        Map<String, Object> axisLabelFormatter = new HashMap<>();
        axisLabelFormatter.put("type", "auto");
        axisLabelFormatter.put("unit", 1);
        axisLabelFormatter.put("suffix", "");
        axisLabelFormatter.put("decimalCount", 2);
        axisLabelFormatter.put("thousandSeparator", true);
        xAxis.put("axisLabelFormatter", axisLabelFormatter);

        customStyle.put("xAxis", xAxis);

        // yAxis等其他customStyle子对象...
        Map<String, Object> yAxis = new HashMap<>();
        // ... 类似xAxis的配置
        customStyle.put("yAxis", yAxis);

        template.put("customStyle", customStyle);

        // 添加senior高级配置
        Map<String, Object> senior = new HashMap<>();
        Map<String, Object> functionCfg = new HashMap<>();
        functionCfg.put("sliderShow", false);
        functionCfg.put("sliderRange", List.of(0, 10));
        functionCfg.put("sliderBg", "#FFFFFF");
        functionCfg.put("sliderFillBg", "#BCD6F1");
        functionCfg.put("sliderTextColor", "#999999");
        functionCfg.put("emptyDataStrategy", "ignoreData");
        functionCfg.put("emptyDataCustomValue", "");
        functionCfg.put("emptyDataFieldCtrl", new ArrayList<>());
        senior.put("functionCfg", functionCfg);

        Map<String, Object> assistLineCfg = new HashMap<>();
        assistLineCfg.put("enable", false);
        assistLineCfg.put("assistLine", new ArrayList<>());
        senior.put("assistLineCfg", assistLineCfg);

        Map<String, Object> threshold = new HashMap<>();
        threshold.put("enable", false);
        threshold.put("gaugeThreshold", "");
        threshold.put("liquidThreshold", "");
        threshold.put("labelThreshold", new ArrayList<>());
        threshold.put("tableThreshold", new ArrayList<>());
        threshold.put("textLabelThreshold", new ArrayList<>());
        senior.put("threshold", threshold);

        Map<String, Object> scrollCfg = new HashMap<>();
        scrollCfg.put("open", false);
        scrollCfg.put("row", 1);
        scrollCfg.put("interval", 2000);
        scrollCfg.put("step", 50);
        senior.put("scrollCfg", scrollCfg);

        senior.put("areaMapping", new HashMap<>());

        Map<String, Object> bubbleCfg = new HashMap<>();
        bubbleCfg.put("enable", false);
        bubbleCfg.put("speed", 1);
        bubbleCfg.put("rings", 1);
        bubbleCfg.put("type", "wave");
        senior.put("bubbleCfg", bubbleCfg);

        template.put("senior", senior);

        // 添加其他固定参数
        template.put("extStack", new ArrayList<>());
        template.put("drillFields", new ArrayList<>());
        template.put("viewFields", new ArrayList<>());
        template.put("extBubble", new ArrayList<>());
        template.put("extLabel", new ArrayList<>());
        template.put("extTooltip", new ArrayList<>());
        template.put("flowMapStartName", new ArrayList<>());
        template.put("flowMapEndName", new ArrayList<>());
        template.put("isPlugin", false);

        // 返回完整模板
        return template;
    }

    // 1. 新增完全独立的折线图模板方法
    private Map<String, Object> getLineChartTemplate() {
        Map<String, Object> template = new HashMap<>();
        // 基本信息
        template.put("sceneId", 0);
        template.put("type", "line");
        template.put("render", "antv");
        template.put("resultMode", "custom");
        template.put("refreshViewEnable", false);
        template.put("refreshTime", 5);
        template.put("refreshUnit", "minute");
        template.put("xAxis", new ArrayList<>());
        template.put("yAxis", new ArrayList<>());
        template.put("xAxisExt", new ArrayList<>());
        template.put("yAxisExt", new ArrayList<>());
        template.put("customFilter", new HashMap<>());
        // customAttr
        Map<String, Object> basicStyle = new HashMap<>();
        basicStyle.put("alpha", 100);
        basicStyle.put("colorScheme", "default");
        basicStyle.put("colors", List.of("#1E90FF", "#90EE90", "#00CED1", "#E2BD84", "#7A90E0", "#3BA272", "#2BE7FF", "#0A8ADA", "#FFD700"));
        basicStyle.put("gradient", false);
        basicStyle.put("lineWidth", 2);
        basicStyle.put("lineSymbol", "circle");
        basicStyle.put("lineSmooth", true);
        basicStyle.put("lineType", "solid");
        basicStyle.put("showLabel", true);
        basicStyle.put("showZoom", true);
        basicStyle.put("autoFit", true);
        // ...可继续补充折线图常用样式参数...
        Map<String, Object> customAttr = new HashMap<>();
        customAttr.put("basicStyle", basicStyle);
        // misc
        Map<String, Object> misc = new HashMap<>();
        misc.put("nameFontSize", 18);
        misc.put("valueFontSize", 18);
        misc.put("showName", true);
        misc.put("nameFontColor", "#ffffff");
        misc.put("valueFontColor", "#5470c6");
        misc.put("hPosition", "center");
        misc.put("vPosition", "center");
        customAttr.put("misc", misc);
        // label
        Map<String, Object> label = new HashMap<>();
        label.put("show", true);
        label.put("color", "#FFFFFF");
        label.put("fontSize", 12);
        label.put("formatter", "");
        Map<String, Object> labelFormatter = new HashMap<>();
        labelFormatter.put("type", "auto");
        labelFormatter.put("unit", 1);
        labelFormatter.put("suffix", "");
        labelFormatter.put("decimalCount", 2);
        labelFormatter.put("thousandSeparator", true);
        label.put("labelFormatter", labelFormatter);
        customAttr.put("label", label);
        // tooltip
        Map<String, Object> tooltip = new HashMap<>();
        tooltip.put("show", true);
        tooltip.put("trigger", "item");
        tooltip.put("confine", true);
        tooltip.put("fontSize", 12);
        tooltip.put("color", "#FFFFFF");
        Map<String, Object> tooltipFormatter = new HashMap<>();
        tooltipFormatter.put("type", "auto");
        tooltipFormatter.put("unit", 1);
        tooltipFormatter.put("suffix", "");
        tooltipFormatter.put("decimalCount", 2);
        tooltipFormatter.put("thousandSeparator", true);
        tooltip.put("tooltipFormatter", tooltipFormatter);
        tooltip.put("backgroundColor", "#5a5c62");
        customAttr.put("tooltip", tooltip);
        // legend
        Map<String, Object> legend = new HashMap<>();
        legend.put("show", true);
        legend.put("hPosition", "center");
        legend.put("vPosition", "bottom");
        legend.put("orient", "horizontal");
        legend.put("icon", "circle");
        legend.put("color", "#FFFFFF");
        legend.put("fontSize", 12);
        customAttr.put("legend", legend);
        // 其他customAttr子对象可根据实际需求补充
        template.put("customAttr", customAttr);
        // customStyle
        Map<String, Object> customStyle = new HashMap<>();
        Map<String, Object> text = new HashMap<>();
        text.put("show", true);
        text.put("fontSize", 16);
        text.put("hPosition", "left");
        text.put("vPosition", "top");
        text.put("isItalic", false);
        text.put("isBolder", true);
        text.put("remarkShow", false);
        text.put("remark", "");
        text.put("fontFamily", "PingFang");
        text.put("letterSpace", "0");
        text.put("fontShadow", false);
        text.put("color", "#FFFFFF");
        text.put("remarkBackgroundColor", "#5A5C62");
        customStyle.put("text", text);
        // xAxis样式
        Map<String, Object> xAxis = new HashMap<>();
        xAxis.put("show", true);
        xAxis.put("position", "bottom");
        xAxis.put("name", "");
        xAxis.put("color", "#FFFFFF");
        xAxis.put("fontSize", 12);
        Map<String, Object> axisLabel = new HashMap<>();
        axisLabel.put("show", true);
        axisLabel.put("color", "#FFFFFF");
        axisLabel.put("fontSize", 12);
        axisLabel.put("rotate", 0);
        axisLabel.put("formatter", "{value}");
        xAxis.put("axisLabel", axisLabel);
        Map<String, Object> axisLine = new HashMap<>();
        axisLine.put("show", true);
        Map<String, Object> lineStyle = new HashMap<>();
        lineStyle.put("color", "#cccccc");
        lineStyle.put("width", 1);
        lineStyle.put("style", "solid");
        axisLine.put("lineStyle", lineStyle);
        xAxis.put("axisLine", axisLine);
        Map<String, Object> splitLine = new HashMap<>();
        splitLine.put("show", false);
        Map<String, Object> splitLineStyle = new HashMap<>();
        splitLineStyle.put("color", "#858383");
        splitLineStyle.put("width", 1);
        splitLineStyle.put("style", "solid");
        splitLine.put("lineStyle", splitLineStyle);
        xAxis.put("splitLine", splitLine);
        Map<String, Object> axisValue = new HashMap<>();
        axisValue.put("auto", true);
        axisValue.put("min", 10);
        axisValue.put("max", 100);
        axisValue.put("split", 10);
        axisValue.put("splitCount", 10);
        xAxis.put("axisValue", axisValue);
        Map<String, Object> axisLabelFormatter = new HashMap<>();
        axisLabelFormatter.put("type", "auto");
        axisLabelFormatter.put("unit", 1);
        axisLabelFormatter.put("suffix", "");
        axisLabelFormatter.put("decimalCount", 2);
        axisLabelFormatter.put("thousandSeparator", true);
        xAxis.put("axisLabelFormatter", axisLabelFormatter);
        customStyle.put("xAxis", xAxis);
        // yAxis样式
        Map<String, Object> yAxis = new HashMap<>();
        yAxis.put("show", true);
        yAxis.put("position", "left");
        yAxis.put("name", "");
        yAxis.put("color", "#FFFFFF");
        yAxis.put("fontSize", 12);
        Map<String, Object> yAxisLabel = new HashMap<>();
        yAxisLabel.put("show", true);
        yAxisLabel.put("color", "#FFFFFF");
        yAxisLabel.put("fontSize", 12);
        yAxisLabel.put("rotate", 0);
        yAxisLabel.put("formatter", "{value}");
        yAxis.put("axisLabel", yAxisLabel);
        Map<String, Object> yAxisLine = new HashMap<>();
        yAxisLine.put("show", false);
        Map<String, Object> yLineStyle = new HashMap<>();
        yLineStyle.put("color", "#858383");
        yLineStyle.put("width", 1);
        yLineStyle.put("style", "solid");
        yAxisLine.put("lineStyle", yLineStyle);
        yAxis.put("axisLine", yAxisLine);
        Map<String, Object> ySplitLine = new HashMap<>();
        ySplitLine.put("show", false);
        Map<String, Object> ySplitLineStyle = new HashMap<>();
        ySplitLineStyle.put("color", "#858383");
        ySplitLineStyle.put("width", 1);
        ySplitLineStyle.put("style", "solid");
        ySplitLine.put("lineStyle", ySplitLineStyle);
        yAxis.put("splitLine", ySplitLine);
        Map<String, Object> yAxisValue = new HashMap<>();
        yAxisValue.put("auto", true);
        yAxisValue.put("min", 10);
        yAxisValue.put("max", 100);
        yAxisValue.put("split", 10);
        yAxisValue.put("splitCount", 10);
        yAxis.put("axisValue", yAxisValue);
        Map<String, Object> yAxisLabelFormatter = new HashMap<>();
        yAxisLabelFormatter.put("type", "auto");
        yAxisLabelFormatter.put("unit", 1);
        yAxisLabelFormatter.put("suffix", "");
        yAxisLabelFormatter.put("decimalCount", 2);
        yAxisLabelFormatter.put("thousandSeparator", true);
        yAxis.put("axisLabelFormatter", yAxisLabelFormatter);
        customStyle.put("yAxis", yAxis);
        template.put("customStyle", customStyle);
        // senior
        Map<String, Object> senior = new HashMap<>();
        Map<String, Object> functionCfg = new HashMap<>();
        functionCfg.put("sliderShow", false);
        functionCfg.put("sliderRange", List.of(0, 10));
        functionCfg.put("sliderBg", "#FFFFFF");
        functionCfg.put("sliderFillBg", "#BCD6F1");
        functionCfg.put("sliderTextColor", "#999999");
        functionCfg.put("emptyDataStrategy", "ignoreData");
        functionCfg.put("emptyDataCustomValue", "");
        functionCfg.put("emptyDataFieldCtrl", new ArrayList<>());
        senior.put("functionCfg", functionCfg);
        Map<String, Object> assistLineCfg = new HashMap<>();
        assistLineCfg.put("enable", false);
        assistLineCfg.put("assistLine", new ArrayList<>());
        senior.put("assistLineCfg", assistLineCfg);
        Map<String, Object> threshold = new HashMap<>();
        threshold.put("enable", false);
        threshold.put("gaugeThreshold", "");
        threshold.put("liquidThreshold", "");
        threshold.put("labelThreshold", new ArrayList<>());
        threshold.put("tableThreshold", new ArrayList<>());
        threshold.put("textLabelThreshold", new ArrayList<>());
        senior.put("threshold", threshold);
        Map<String, Object> scrollCfg = new HashMap<>();
        scrollCfg.put("open", false);
        scrollCfg.put("row", 1);
        scrollCfg.put("interval", 2000);
        scrollCfg.put("step", 50);
        senior.put("scrollCfg", scrollCfg);
        senior.put("areaMapping", new HashMap<>());
        Map<String, Object> bubbleCfg = new HashMap<>();
        bubbleCfg.put("enable", false);
        bubbleCfg.put("speed", 1);
        bubbleCfg.put("rings", 1);
        bubbleCfg.put("type", "wave");
        senior.put("bubbleCfg", bubbleCfg);
        template.put("senior", senior);
        // 其他固定参数
        template.put("extStack", new ArrayList<>());
        template.put("drillFields", new ArrayList<>());
        template.put("viewFields", new ArrayList<>());
        template.put("extBubble", new ArrayList<>());
        template.put("extLabel", new ArrayList<>());
        template.put("extTooltip", new ArrayList<>());
        template.put("flowMapStartName", new ArrayList<>());
        template.put("flowMapEndName", new ArrayList<>());
        template.put("isPlugin", false);
        return template;
    }

    /**
     * 获取条形图模板，包含所有固定参数
     */
    private Map<String, Object> getBarHorizontalChartTemplate() {
        Map<String, Object> template = new HashMap<>();

        // 基本信息（部分动态，但提供默认值）
        template.put("sceneId", 0);
        template.put("type", "bar-horizontal");
        template.put("render", "antv");
        template.put("resultMode", "custom");
        template.put("refreshViewEnable", false);
        template.put("refreshTime", 5);
        template.put("refreshUnit", "minute");

        // 占位符（将被动态参数覆盖）
        template.put("xAxis", new ArrayList<>());
        template.put("yAxis", new ArrayList<>());
        template.put("xAxisExt", new ArrayList<>());
        template.put("yAxisExt", new ArrayList<>());
        template.put("customFilter", new HashMap<>());

        // 图表固定参数 - basicStyle
        Map<String, Object> basicStyle = new HashMap<>();
        basicStyle.put("alpha", 100);
        basicStyle.put("tableBorderColor", "#CCCCCC");
        basicStyle.put("tableScrollBarColor", "rgba(255, 255, 255, 0.5)");
        basicStyle.put("tableColumnMode", "adapt");
        basicStyle.put("tableColumnWidth", 100);
        basicStyle.put("tableFieldWidth", new ArrayList<>());
        basicStyle.put("tablePageMode", "page");
        basicStyle.put("tablePageStyle", "simple");
        basicStyle.put("tablePageSize", 20);
        basicStyle.put("gaugeStyle", "default");
        basicStyle.put("colorScheme", "default");
        basicStyle.put("colors", List.of("#1E90FF", "#90EE90", "#00CED1", "#E2BD84", "#7A90E0", "#3BA272", "#2BE7FF", "#0A8ADA", "#FFD700"));
        basicStyle.put("mapVendor", "amap");
        basicStyle.put("gradient", false);
        basicStyle.put("lineWidth", 2);
        basicStyle.put("lineSymbol", "circle");
        basicStyle.put("lineSymbolSize", 4);
        basicStyle.put("lineSmooth", true);
        basicStyle.put("barDefault", true);
        basicStyle.put("radiusColumnBar", "rightAngle");
        basicStyle.put("columnBarRightAngleRadius", 20);
        basicStyle.put("barWidth", 40);
        basicStyle.put("barGap", 0.4);
        basicStyle.put("lineType", "solid");
        basicStyle.put("scatterSymbol", "circle");
        basicStyle.put("scatterSymbolSize", 8);
        basicStyle.put("radarShape", "polygon");
        basicStyle.put("mapStyle", "darkblue");
        basicStyle.put("heatMapType", "heatmap");
        basicStyle.put("heatMapIntensity", 2);
        basicStyle.put("heatMapRadius", 20);
        basicStyle.put("areaBorderColor", "#EBEEF5");
        basicStyle.put("areaBaseColor", "#5470C6");
        basicStyle.put("mapSymbolOpacity", 0.7);
        basicStyle.put("mapSymbolStrokeWidth", 2);
        basicStyle.put("mapSymbol", "circle");
        basicStyle.put("mapSymbolSize", 6);
        basicStyle.put("radius", 80);
        basicStyle.put("innerRadius", 60);
        basicStyle.put("showZoom", true);
        basicStyle.put("zoomButtonColor", "#fff");
        basicStyle.put("zoomBackground", "#000");
        basicStyle.put("tableLayoutMode", "grid");
        basicStyle.put("calcTopN", false);
        basicStyle.put("topN", 5);
        basicStyle.put("topNLabel", "其他");
        basicStyle.put("gaugeAxisLine", true);
        basicStyle.put("gaugePercentLabel", true);
        basicStyle.put("showSummary", false);
        basicStyle.put("summaryLabel", "总计");
        basicStyle.put("seriesColor", new ArrayList<>());
        basicStyle.put("layout", "horizontal");
        basicStyle.put("mapSymbolSizeMin", 4);
        basicStyle.put("mapSymbolSizeMax", 30);
        basicStyle.put("showLabel", true);
        basicStyle.put("mapStyleUrl", "");
        basicStyle.put("autoFit", true);
        basicStyle.put("mapCenter", Map.of("longitude", 117.232, "latitude", 39.354));
        basicStyle.put("zoomLevel", 7);
        basicStyle.put("customIcon", "");
        basicStyle.put("showHoverStyle", true);

        // 图表固定参数 - misc
        Map<String, Object> misc = new HashMap<>();
        misc.put("pieInnerRadius", 0);
        misc.put("pieOuterRadius", 80);
        misc.put("radarShape", "polygon");
        misc.put("radarSize", 80);
        misc.put("gaugeMinType", "fix");
        misc.put("gaugeMinField", Map.of("id", "", "summary", ""));
        misc.put("gaugeMin", 0);
        misc.put("gaugeMaxType", "dynamic");
        misc.put("gaugeMaxField", Map.of("id", "", "summary", ""));
        misc.put("gaugeStartAngle", 225);
        misc.put("gaugeEndAngle", -45);
        misc.put("nameFontSize", 18);
        misc.put("valueFontSize", 18);
        misc.put("nameValueSpace", 10);
        misc.put("valueFontColor", "#5470c6");
        misc.put("valueFontFamily", "Microsoft YaHei");
        misc.put("valueFontIsBolder", false);
        misc.put("valueFontIsItalic", false);
        misc.put("valueLetterSpace", 0);
        misc.put("valueFontShadow", false);
        misc.put("showName", true);
        misc.put("nameFontColor", "#ffffff");
        misc.put("nameFontFamily", "Microsoft YaHei");
        misc.put("nameFontIsBolder", false);
        misc.put("nameFontIsItalic", false);
        misc.put("nameLetterSpace", "0");
        misc.put("nameFontShadow", false);
        misc.put("treemapWidth", 80);
        misc.put("treemapHeight", 80);
        misc.put("liquidMaxType", "dynamic");
        misc.put("liquidMaxField", Map.of("id", "", "summary", ""));
        misc.put("liquidSize", 80);
        misc.put("liquidShape", "circle");
        misc.put("hPosition", "center");
        misc.put("vPosition", "center");
        misc.put("mapPitch", 0);
        misc.put("wordSizeRange", List.of(8, 32));
        misc.put("wordSpacing", 6);
        misc.put("mapAutoLegend", true);
        misc.put("mapLegendMax", 0);
        misc.put("mapLegendMin", 0);
        misc.put("mapLegendNumber", 9);
        misc.put("mapLegendRangeType", "quantize");
        misc.put("mapLegendCustomRange", new ArrayList<>());

        // flowMapConfig
        Map<String, Object> flowMapConfig = new HashMap<>();
        Map<String, Object> lineConfig = new HashMap<>();
        lineConfig.put("mapLineAnimate", true);
        lineConfig.put("mapLineType", "arc");
        lineConfig.put("mapLineWidth", 1);
        lineConfig.put("mapLineAnimateDuration", 3);
        lineConfig.put("mapLineGradient", false);
        lineConfig.put("mapLineSourceColor", "#146C94");
        lineConfig.put("mapLineTargetColor", "#576CBC");
        lineConfig.put("alpha", 100);
        flowMapConfig.put("lineConfig", lineConfig);

        Map<String, Object> pointConfig = new HashMap<>();
        pointConfig.put("text", Map.of("color", "#146C94", "fontSize", 10));
        pointConfig.put("point", Map.of("color", "#146C94", "size", 4, "animate", false, "speed", 0.01));
        flowMapConfig.put("pointConfig", pointConfig);
        misc.put("flowMapConfig", flowMapConfig);

        misc.put("wordCloudAxisValueRange", Map.of("auto", true, "min", 0, "max", 0));

        // 图表固定参数 - label
        Map<String, Object> label = new HashMap<>();
        label.put("show", false);
        label.put("childrenShow", true);
        label.put("position", "middle");
        label.put("color", "#FFFFFF");
        label.put("fontSize", 12);
        label.put("formatter", "");
        label.put("labelLine", Map.of("show", true));
        label.put("labelFormatter", Map.of("type", "auto", "unit", 1, "suffix", "", "decimalCount", 2, "thousandSeparator", true));
        label.put("reserveDecimalCount", 2);
        label.put("labelShadow", false);
        label.put("labelBgColor", "");
        label.put("labelShadowColor", "");
        label.put("quotaLabelFormatter", Map.of("type", "auto", "unit", 1, "suffix", "", "decimalCount", 2, "thousandSeparator", true));
        label.put("showDimension", true);
        label.put("showQuota", false);
        label.put("showProportion", true);
        label.put("seriesLabelFormatter", new ArrayList<>());
        label.put("conversionTag", Map.of("show", false, "precision", 2, "text", "转化率"));
        label.put("showTotal", false);
        label.put("totalFontSize", 12);
        label.put("totalColor", "#FFF");
        label.put("totalFormatter", Map.of("type", "auto", "unit", 1, "suffix", "", "decimalCount", 2, "thousandSeparator", true));
        label.put("showStackQuota", false);
        label.put("fullDisplay", false);
        label.put("showFields", new ArrayList<>());

        // 图表固定参数 - tooltip
        Map<String, Object> tooltip = new HashMap<>();
        tooltip.put("show", true);
        tooltip.put("trigger", "item");
        tooltip.put("confine", true);
        tooltip.put("fontSize", 12);
        tooltip.put("color", "#FFFFFF");
        tooltip.put("tooltipFormatter", Map.of("type", "auto", "unit", 1, "suffix", "", "decimalCount", 2, "thousandSeparator", true));
        tooltip.put("backgroundColor", "#5a5c62");
        tooltip.put("seriesTooltipFormatter", new ArrayList<>());
        tooltip.put("carousel", Map.of("enable", false, "stayTime", 3, "intervalTime", 0));
        tooltip.put("showFields", new ArrayList<>());

        // 聚合customAttr
        Map<String, Object> customAttr = new HashMap<>();
        customAttr.put("basicStyle", basicStyle);
        customAttr.put("misc", misc);
        customAttr.put("label", label);
        customAttr.put("tooltip", tooltip);

        // tableTotal
        Map<String, Object> tableTotal = new HashMap<>();
        Map<String, Object> row = new HashMap<>();
        row.put("showGrandTotals", true);
        row.put("showSubTotals", true);
        row.put("reverseLayout", false);
        row.put("reverseSubLayout", false);
        row.put("label", "总计");
        row.put("subLabel", "小计");
        row.put("subTotalsDimensions", new ArrayList<>());
        row.put("calcTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        row.put("calcSubTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        row.put("totalSort", "none");
        row.put("totalSortField", "");
        tableTotal.put("row", row);

        Map<String, Object> col = new HashMap<>();
        col.put("showGrandTotals", true);
        col.put("showSubTotals", true);
        col.put("reverseLayout", false);
        col.put("reverseSubLayout", false);
        col.put("label", "总计");
        col.put("subLabel", "小计");
        col.put("subTotalsDimensions", new ArrayList<>());
        col.put("calcTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        col.put("calcSubTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        col.put("totalSort", "none");
        col.put("totalSortField", "");
        tableTotal.put("col", col);
        customAttr.put("tableTotal", tableTotal);

        // tableHeader
        Map<String, Object> tableHeader = new HashMap<>();
        tableHeader.put("indexLabel", "序号");
        tableHeader.put("showIndex", false);
        tableHeader.put("tableHeaderAlign", "left");
        tableHeader.put("tableHeaderBgColor", "#1E90FF");
        tableHeader.put("tableHeaderFontColor", "#FFFFFF");
        tableHeader.put("tableTitleFontSize", 12);
        tableHeader.put("tableTitleHeight", 36);
        tableHeader.put("tableHeaderSort", false);
        tableHeader.put("showColTooltip", false);
        tableHeader.put("showRowTooltip", false);
        tableHeader.put("showTableHeader", true);
        tableHeader.put("showHorizonBorder", true);
        tableHeader.put("showVerticalBorder", true);
        tableHeader.put("isItalic", false);
        tableHeader.put("isBolder", true);
        customAttr.put("tableHeader", tableHeader);

        // tableCell
        Map<String, Object> tableCell = new HashMap<>();
        tableCell.put("tableFontColor", "#ffffff");
        tableCell.put("tableItemAlign", "right");
        tableCell.put("tableItemBgColor", "#131E42");
        tableCell.put("tableItemFontSize", 12);
        tableCell.put("tableItemHeight", 36);
        tableCell.put("enableTableCrossBG", false);
        tableCell.put("tableItemSubBgColor", "#EEEEEE");
        tableCell.put("showTooltip", false);
        tableCell.put("showHorizonBorder", true);
        tableCell.put("showVerticalBorder", true);
        tableCell.put("isItalic", false);
        tableCell.put("isBolder", false);
        tableCell.put("tableFreeze", false);
        tableCell.put("tableColumnFreezeHead", 0);
        tableCell.put("tableRowFreezeHead", 0);
        tableCell.put("mergeCells", true);
        customAttr.put("tableCell", tableCell);

        template.put("customAttr", customAttr);

        // 添加customStyle部分
        Map<String, Object> customStyle = new HashMap<>();

        // text
        Map<String, Object> text = new HashMap<>();
        text.put("show", true);
        text.put("fontSize", 16);
        text.put("hPosition", "left");
        text.put("vPosition", "top");
        text.put("isItalic", false);
        text.put("isBolder", true);
        text.put("remarkShow", false);
        text.put("remark", "");
        text.put("fontFamily", "PingFang");
        text.put("letterSpace", "0");
        text.put("fontShadow", false);
        text.put("color", "#FFFFFF");
        text.put("remarkBackgroundColor", "#5A5C62");
        customStyle.put("text", text);

        // legend
        Map<String, Object> legend = new HashMap<>();
        legend.put("show", true);
        legend.put("hPosition", "center");
        legend.put("vPosition", "bottom");
        legend.put("orient", "horizontal");
        legend.put("icon", "circle");
        legend.put("color", "#FFFFFF");
        legend.put("fontSize", 12);
        customStyle.put("legend", legend);

        // xAxis
        Map<String, Object> xAxis = new HashMap<>();
        xAxis.put("show", true);
        xAxis.put("position", "bottom");
        xAxis.put("name", "");
        xAxis.put("color", "#FFFFFF");
        xAxis.put("fontSize", 12);
        Map<String, Object> axisLabel = new HashMap<>();
        axisLabel.put("show", true);
        axisLabel.put("color", "#FFFFFF");
        axisLabel.put("fontSize", 12);
        axisLabel.put("rotate", 0);
        axisLabel.put("formatter", "{value}");
        xAxis.put("axisLabel", axisLabel);
        Map<String, Object> axisLine = new HashMap<>();
        axisLine.put("show", true);
        Map<String, Object> lineStyle = new HashMap<>();
        lineStyle.put("color", "#cccccc");
        lineStyle.put("width", 1);
        lineStyle.put("style", "solid");
        axisLine.put("lineStyle", lineStyle);
        xAxis.put("axisLine", axisLine);
        Map<String, Object> splitLine = new HashMap<>();
        splitLine.put("show", false);
        Map<String, Object> splitLineStyle = new HashMap<>();
        splitLineStyle.put("color", "#858383");
        splitLineStyle.put("width", 1);
        splitLineStyle.put("style", "solid");
        splitLine.put("lineStyle", splitLineStyle);
        xAxis.put("splitLine", splitLine);
        Map<String, Object> axisValue = new HashMap<>();
        axisValue.put("auto", true);
        axisValue.put("min", 10);
        axisValue.put("max", 100);
        axisValue.put("split", 10);
        axisValue.put("splitCount", 10);
        xAxis.put("axisValue", axisValue);
        Map<String, Object> axisLabelFormatter = new HashMap<>();
        axisLabelFormatter.put("type", "auto");
        axisLabelFormatter.put("unit", 1);
        axisLabelFormatter.put("suffix", "");
        axisLabelFormatter.put("decimalCount", 2);
        axisLabelFormatter.put("thousandSeparator", true);
        xAxis.put("axisLabelFormatter", axisLabelFormatter);
        customStyle.put("xAxis", xAxis);

        // yAxis
        Map<String, Object> yAxis = new HashMap<>();
        yAxis.put("show", true);
        yAxis.put("position", "left");
        yAxis.put("name", "");
        yAxis.put("color", "#FFFFFF");
        yAxis.put("fontSize", 12);
        Map<String, Object> yAxisLabel = new HashMap<>();
        yAxisLabel.put("show", true);
        yAxisLabel.put("color", "#FFFFFF");
        yAxisLabel.put("fontSize", 12);
        yAxisLabel.put("rotate", 0);
        yAxisLabel.put("formatter", "{value}");
        yAxis.put("axisLabel", yAxisLabel);
        Map<String, Object> yAxisLine = new HashMap<>();
        yAxisLine.put("show", false);
        Map<String, Object> yLineStyle = new HashMap<>();
        yLineStyle.put("color", "#858383");
        yLineStyle.put("width", 1);
        yLineStyle.put("style", "solid");
        yAxisLine.put("lineStyle", yLineStyle);
        yAxis.put("axisLine", yAxisLine);
        Map<String, Object> ySplitLine = new HashMap<>();
        ySplitLine.put("show", true);
        Map<String, Object> ySplitLineStyle = new HashMap<>();
        ySplitLineStyle.put("color", "#858383");
        ySplitLineStyle.put("width", 1);
        ySplitLineStyle.put("style", "solid");
        ySplitLine.put("lineStyle", ySplitLineStyle);
        yAxis.put("splitLine", ySplitLine);
        Map<String, Object> yAxisValue = new HashMap<>();
        yAxisValue.put("auto", true);
        yAxisValue.put("min", 10);
        yAxisValue.put("max", 100);
        yAxisValue.put("split", 10);
        yAxisValue.put("splitCount", 10);
        yAxis.put("axisValue", yAxisValue);
        Map<String, Object> yAxisLabelFormatter = new HashMap<>();
        yAxisLabelFormatter.put("type", "auto");
        yAxisLabelFormatter.put("unit", 1);
        yAxisLabelFormatter.put("suffix", "");
        yAxisLabelFormatter.put("decimalCount", 2);
        yAxisLabelFormatter.put("thousandSeparator", true);
        yAxis.put("axisLabelFormatter", yAxisLabelFormatter);
        customStyle.put("yAxis", yAxis);
        template.put("customStyle", customStyle);

        // senior
        Map<String, Object> senior = new HashMap<>();
        Map<String, Object> functionCfg = new HashMap<>();
        functionCfg.put("sliderShow", false);
        functionCfg.put("sliderRange", List.of(0, 10));
        functionCfg.put("sliderBg", "#FFFFFF");
        functionCfg.put("sliderFillBg", "#BCD6F1");
        functionCfg.put("sliderTextColor", "#999999");
        functionCfg.put("emptyDataStrategy", "ignoreData");
        functionCfg.put("emptyDataCustomValue", "");
        functionCfg.put("emptyDataFieldCtrl", new ArrayList<>());
        senior.put("functionCfg", functionCfg);
        Map<String, Object> assistLineCfg = new HashMap<>();
        assistLineCfg.put("enable", false);
        assistLineCfg.put("assistLine", new ArrayList<>());
        senior.put("assistLineCfg", assistLineCfg);
        Map<String, Object> threshold = new HashMap<>();
        threshold.put("enable", false);
        threshold.put("gaugeThreshold", "");
        threshold.put("liquidThreshold", "");
        threshold.put("labelThreshold", new ArrayList<>());
        threshold.put("tableThreshold", new ArrayList<>());
        threshold.put("textLabelThreshold", new ArrayList<>());
        senior.put("threshold", threshold);
        Map<String, Object> scrollCfg = new HashMap<>();
        scrollCfg.put("open", false);
        scrollCfg.put("row", 1);
        scrollCfg.put("interval", 2000);
        scrollCfg.put("step", 50);
        senior.put("scrollCfg", scrollCfg);
        senior.put("areaMapping", new HashMap<>());
        Map<String, Object> bubbleCfg = new HashMap<>();
        bubbleCfg.put("enable", false);
        bubbleCfg.put("speed", 1);
        bubbleCfg.put("rings", 1);
        bubbleCfg.put("type", "wave");
        senior.put("bubbleCfg", bubbleCfg);
        template.put("senior", senior);

        // 其他固定参数
        template.put("extStack", new ArrayList<>());
        template.put("drillFields", new ArrayList<>());
        template.put("viewFields", new ArrayList<>());
        template.put("extBubble", new ArrayList<>());
        template.put("extLabel", new ArrayList<>());
        template.put("extTooltip", new ArrayList<>());
        template.put("flowMapStartName", new ArrayList<>());
        template.put("flowMapEndName", new ArrayList<>());
        template.put("isPlugin", false);

        return template;
    }

private Map<String, Object> getPieChartTemplate() {
        Map<String, Object> template = new HashMap<>();

        // 基本信息
        template.put("sceneId", 0);
        template.put("type", "pie");
        template.put("render", "antv");
        template.put("resultMode", "custom");
        template.put("refreshViewEnable", false);
        template.put("refreshTime", 5);
        template.put("refreshUnit", "minute");

        // 占位符（将被动态参数覆盖）
        template.put("xAxis", new ArrayList<>());
        template.put("yAxis", new ArrayList<>());
        template.put("xAxisExt", new ArrayList<>());
        template.put("yAxisExt", new ArrayList<>());
        template.put("customFilter", new HashMap<>());

        // 图表固定参数 - basicStyle
        Map<String, Object> basicStyle = new HashMap<>();
        basicStyle.put("alpha", 100);
        basicStyle.put("tableBorderColor", "#CCCCCC");
        basicStyle.put("tableScrollBarColor", "rgba(255, 255, 255, 0.5)");
        basicStyle.put("tableColumnMode", "adapt");
        basicStyle.put("tableColumnWidth", 100);
        basicStyle.put("tableFieldWidth", new ArrayList<>());
        basicStyle.put("tablePageMode", "page");
        basicStyle.put("tablePageStyle", "simple");
        basicStyle.put("tablePageSize", 20);
        basicStyle.put("gaugeStyle", "default");
        basicStyle.put("colorScheme", "default");
        basicStyle.put("colors", Arrays.asList("#1E90FF", "#90EE90", "#00CED1", "#E2BD84", "#7A90E0", "#3BA272", "#2BE7FF", "#0A8ADA", "#FFD700"));
        basicStyle.put("mapVendor", "amap");
        basicStyle.put("gradient", false);
        basicStyle.put("lineWidth", 2);
        basicStyle.put("lineSymbol", "circle");
        basicStyle.put("lineSymbolSize", 4);
        basicStyle.put("lineSmooth", true);
        basicStyle.put("barDefault", true);
        basicStyle.put("radiusColumnBar", "rightAngle");
        basicStyle.put("columnBarRightAngleRadius", 20);
        basicStyle.put("barWidth", 40);
        basicStyle.put("barGap", 0.4);
        basicStyle.put("lineType", "solid");
        basicStyle.put("scatterSymbol", "circle");
        basicStyle.put("scatterSymbolSize", 8);
        basicStyle.put("radarShape", "polygon");
        basicStyle.put("mapStyle", "darkblue");
        basicStyle.put("heatMapType", "heatmap");
        basicStyle.put("heatMapIntensity", 2);
        basicStyle.put("heatMapRadius", 20);
        basicStyle.put("areaBorderColor", "#EBEEF5");
        basicStyle.put("areaBaseColor", "#5470C6");
        basicStyle.put("mapSymbolOpacity", 0.7);
        basicStyle.put("mapSymbolStrokeWidth", 2);
        basicStyle.put("mapSymbol", "circle");
        basicStyle.put("mapSymbolSize", 6);
        basicStyle.put("radius", 80);
        basicStyle.put("innerRadius", 60);
        basicStyle.put("showZoom", true);
        basicStyle.put("zoomButtonColor", "#fff");
        basicStyle.put("zoomBackground", "#000");
        basicStyle.put("tableLayoutMode", "grid");
        basicStyle.put("calcTopN", false);
        basicStyle.put("topN", 5);
        basicStyle.put("topNLabel", "其他");
        basicStyle.put("gaugeAxisLine", true);
        basicStyle.put("gaugePercentLabel", true);
        basicStyle.put("showSummary", false);
        basicStyle.put("summaryLabel", "总计");
        basicStyle.put("seriesColor", new ArrayList<>());
        basicStyle.put("layout", "horizontal");
        basicStyle.put("mapSymbolSizeMin", 4);
        basicStyle.put("mapSymbolSizeMax", 30);
        basicStyle.put("showLabel", true);
        basicStyle.put("mapStyleUrl", "");
        basicStyle.put("autoFit", true);
        basicStyle.put("mapCenter", Map.of("longitude", 117.232, "latitude", 39.354));
        basicStyle.put("zoomLevel", 7);
        basicStyle.put("customIcon", "");
        basicStyle.put("showHoverStyle", true);

        // 图表固定参数 - misc (饼图特有配置)
        Map<String, Object> misc = new HashMap<>();
        misc.put("pieInnerRadius", 0);
        misc.put("pieOuterRadius", 80);
        misc.put("radarShape", "polygon");
        misc.put("radarSize", 80);
        misc.put("gaugeMinType", "fix");
        misc.put("gaugeMinField", Map.of("id", "", "summary", ""));
        misc.put("gaugeMin", 0);
        misc.put("gaugeMaxType", "dynamic");
        misc.put("gaugeMaxField", Map.of("id", "", "summary", ""));
        misc.put("gaugeStartAngle", 225);
        misc.put("gaugeEndAngle", -45);
        misc.put("nameFontSize", 18);
        misc.put("valueFontSize", 18);
        misc.put("nameValueSpace", 10);
        misc.put("valueFontColor", "#5470c6");
        misc.put("valueFontFamily", "Microsoft YaHei");
        misc.put("valueFontIsBolder", false);
        misc.put("valueFontIsItalic", false);
        misc.put("valueLetterSpace", 0);
        misc.put("valueFontShadow", false);
        misc.put("showName", true);
        misc.put("nameFontColor", "#ffffff");
        misc.put("nameFontFamily", "Microsoft YaHei");
        misc.put("nameFontIsBolder", false);
        misc.put("nameFontIsItalic", false);
        misc.put("nameLetterSpace", "0");
        misc.put("nameFontShadow", false);
        misc.put("treemapWidth", 80);
        misc.put("treemapHeight", 80);
        misc.put("liquidMaxType", "dynamic");
        misc.put("liquidMaxField", Map.of("id", "", "summary", ""));
        misc.put("liquidSize", 80);
        misc.put("liquidShape", "circle");
        misc.put("hPosition", "center");
        misc.put("vPosition", "center");
        misc.put("mapPitch", 0);
        misc.put("wordSizeRange", Arrays.asList(8, 32));
        misc.put("wordSpacing", 6);
        misc.put("mapAutoLegend", true);
        misc.put("mapLegendMax", 0);
        misc.put("mapLegendMin", 0);
        misc.put("mapLegendNumber", 9);
        misc.put("mapLegendRangeType", "quantize");
        misc.put("mapLegendCustomRange", new ArrayList<>());
        misc.put("flowMapConfig", Map.of(
        "lineConfig", Map.of(
        "mapLineAnimate", true,
        "mapLineType", "arc",
        "mapLineWidth", 1,
        "mapLineAnimateDuration", 3,
        "mapLineGradient", false,
        "mapLineSourceColor", "#146C94",
        "mapLineTargetColor", "#576CBC",
        "alpha", 100
        ),
        "pointConfig", Map.of(
        "text", Map.of(
        "color", "#146C94",
        "fontSize", 10
        ),
        "point", Map.of(
        "color", "#146C94",
        "size", 4,
        "animate", false,
        "speed", 0.01
        )
        )
        ));
        misc.put("wordCloudAxisValueRange", Map.of("auto", true, "min", 0, "max", 0));

        // 图表固定参数 - label
        Map<String, Object> label = new HashMap<>();
        label.put("show", true);
        label.put("childrenShow", true);
        label.put("position", "outer");
        label.put("color", "#FFFFFF");
        label.put("fontSize", 12);
        label.put("formatter", "");
        label.put("labelLine", Map.of("show", true));
        label.put("labelFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        label.put("reserveDecimalCount", 2);
        label.put("labelShadow", false);
        label.put("labelBgColor", "");
        label.put("labelShadowColor", "");
        label.put("quotaLabelFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        label.put("showDimension", true);
        label.put("showQuota", false);
        label.put("showProportion", true);
        label.put("seriesLabelFormatter", new ArrayList<>());
        label.put("conversionTag", Map.of(
        "show", false,
        "precision", 2,
        "text", "转化率"
        ));
        label.put("showTotal", false);
        label.put("totalFontSize", 12);
        label.put("totalColor", "#FFF");
        label.put("totalFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        label.put("showStackQuota", false);
        label.put("fullDisplay", false);
        label.put("showFields", new ArrayList<>());

        // 图表固定参数 - tooltip
        Map<String, Object> tooltip = new HashMap<>();
        tooltip.put("show", true);
        tooltip.put("trigger", "item");
        tooltip.put("confine", true);
        tooltip.put("fontSize", 12);
        tooltip.put("color", "#FFFFFF");
        tooltip.put("tooltipFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        tooltip.put("backgroundColor", "#5a5c62");
        tooltip.put("seriesTooltipFormatter", new ArrayList<>());
        tooltip.put("carousel", Map.of(
        "enable", false,
        "stayTime", 3,
        "intervalTime", 0
        ));
        tooltip.put("showFields", new ArrayList<>());

        // 图表固定参数 - tableTotal
        Map<String, Object> tableTotal = new HashMap<>();
        Map<String, Object> row = new HashMap<>();
        row.put("showGrandTotals", true);
        row.put("showSubTotals", true);
        row.put("reverseLayout", false);
        row.put("reverseSubLayout", false);
        row.put("label", "总计");
        row.put("subLabel", "小计");
        row.put("subTotalsDimensions", new ArrayList<>());
        row.put("calcTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        row.put("calcSubTotals", Map.of("aggregation", "SUM", "cfg", new ArrayList<>()));
        row.put("totalSort", "none");
        row.put("totalSortField", "");
        tableTotal.put("row", row);

        Map<String, Object> col = new HashMap<>(row);
        tableTotal.put("col", col);

        // 图表固定参数 - tableHeader
        Map<String, Object> tableHeader = new HashMap<>();
        tableHeader.put("indexLabel", "序号");
        tableHeader.put("showIndex", false);
        tableHeader.put("tableHeaderAlign", "left");
        tableHeader.put("tableHeaderBgColor", "#1E90FF");
        tableHeader.put("tableHeaderFontColor", "#FFFFFF");
        tableHeader.put("tableTitleFontSize", 12);
        tableHeader.put("tableTitleHeight", 36);
        tableHeader.put("tableHeaderSort", false);
        tableHeader.put("showColTooltip", false);
        tableHeader.put("showRowTooltip", false);
        tableHeader.put("showTableHeader", true);
        tableHeader.put("showHorizonBorder", true);
        tableHeader.put("showVerticalBorder", true);
        tableHeader.put("isItalic", false);
        tableHeader.put("isBolder", true);

        // 图表固定参数 - tableCell
        Map<String, Object> tableCell = new HashMap<>();
        tableCell.put("tableFontColor", "#ffffff");
        tableCell.put("tableItemAlign", "right");
        tableCell.put("tableItemBgColor", "#131E42");
        tableCell.put("tableItemFontSize", 12);
        tableCell.put("tableItemHeight", 36);
        tableCell.put("enableTableCrossBG", false);
        tableCell.put("tableItemSubBgColor", "#EEEEEE");
        tableCell.put("showTooltip", false);
        tableCell.put("showHorizonBorder", true);
        tableCell.put("showVerticalBorder", true);
        tableCell.put("isItalic", false);
        tableCell.put("isBolder", false);
        tableCell.put("tableFreeze", false);
        tableCell.put("tableColumnFreezeHead", 0);
        tableCell.put("tableRowFreezeHead", 0);
        tableCell.put("mergeCells", true);

        // 图表固定参数 - indicator
        Map<String, Object> indicator = new HashMap<>();
        indicator.put("show", true);
        indicator.put("fontSize", 20);
        indicator.put("color", "#5470C6ff");
        indicator.put("hPosition", "center");
        indicator.put("vPosition", "center");
        indicator.put("isItalic", false);
        indicator.put("isBolder", true);
        indicator.put("fontFamily", "Microsoft YaHei");
        indicator.put("letterSpace", 0);
        indicator.put("fontShadow", false);
        indicator.put("backgroundColor", "");
        indicator.put("suffixEnable", true);
        indicator.put("suffix", "");
        indicator.put("suffixFontSize", 14);
        indicator.put("suffixColor", "#5470C6ff");
        indicator.put("suffixIsItalic", false);
        indicator.put("suffixIsBolder", true);
        indicator.put("suffixFontFamily", "Microsoft YaHei");
        indicator.put("suffixLetterSpace", 0);
        indicator.put("suffixFontShadow", false);

        // 图表固定参数 - indicatorName
        Map<String, Object> indicatorName = new HashMap<>();
        indicatorName.put("show", true);
        indicatorName.put("fontSize", 18);
        indicatorName.put("color", "#FFFFFF");
        indicatorName.put("isItalic", false);
        indicatorName.put("isBolder", true);
        indicatorName.put("fontFamily", "Microsoft YaHei");
        indicatorName.put("letterSpace", 0);
        indicatorName.put("fontShadow", false);
        indicatorName.put("nameValueSpacing", 0);

        // 图表固定参数 - map
        Map<String, Object> map = new HashMap<>();
        map.put("id", "");
        map.put("level", "world");

        // 合并所有样式参数
        Map<String, Object> customAttr = new HashMap<>();
        customAttr.put("basicStyle", basicStyle);
        customAttr.put("misc", misc);
        customAttr.put("label", label);
        customAttr.put("tooltip", tooltip);
        customAttr.put("tableTotal", tableTotal);
        customAttr.put("tableHeader", tableHeader);
        customAttr.put("tableCell", tableCell);
        customAttr.put("indicator", indicator);
        customAttr.put("indicatorName", indicatorName);
        customAttr.put("map", map);

        template.put("customAttr", customAttr);

        // 图表固定参数 - customStyle
        Map<String, Object> customStyle = new HashMap<>();
        // text
        Map<String, Object> text = new HashMap<>();
        text.put("show", true);
        text.put("fontSize", 16);
        text.put("hPosition", "left");
        text.put("vPosition", "top");
        text.put("isItalic", false);
        text.put("isBolder", true);
        text.put("remarkShow", false);
        text.put("remark", "");
        text.put("fontFamily", "PingFang");
        text.put("letterSpace", "0");
        text.put("fontShadow", false);
        text.put("color", "#FFFFFF");
        text.put("remarkBackgroundColor", "#5A5C62");
        customStyle.put("text", text);

        // legend
        Map<String, Object> legend = new HashMap<>();
        legend.put("show", false);
        legend.put("hPosition", "center");
        legend.put("vPosition", "bottom");
        legend.put("orient", "horizontal");
        legend.put("icon", "circle");
        legend.put("color", "#FFFFFF");
        legend.put("fontSize", 12);
        customStyle.put("legend", legend);

        // xAxis
        Map<String, Object> xAxis = new HashMap<>();
        xAxis.put("show", true);
        xAxis.put("position", "bottom");
        xAxis.put("name", "");
        xAxis.put("color", "#FFFFFF");
        xAxis.put("fontSize", 12);
        xAxis.put("axisLabel", Map.of(
        "show", true,
        "color", "#FFFFFF",
        "fontSize", 12,
        "rotate", 0,
        "formatter", "{value}"
        ));
        xAxis.put("axisLine", Map.of(
        "show", true,
        "lineStyle", Map.of(
        "color", "#cccccc",
        "width", 1,
        "style", "solid"
        )
        ));
        xAxis.put("splitLine", Map.of(
        "show", false,
        "lineStyle", Map.of(
        "color", "#858383",
        "width", 1,
        "style", "solid"
        )
        ));
        xAxis.put("axisValue", Map.of(
        "auto", true,
        "min", 10,
        "max", 100,
        "split", 10,
        "splitCount", 10
        ));
        xAxis.put("axisLabelFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        customStyle.put("xAxis", xAxis);

        // yAxis
        Map<String, Object> yAxis = new HashMap<>();
        yAxis.put("show", true);
        yAxis.put("position", "left");
        yAxis.put("name", "");
        yAxis.put("color", "#FFFFFF");
        yAxis.put("fontSize", 12);
        yAxis.put("axisLabel", Map.of(
        "show", true,
        "color", "#FFFFFF",
        "fontSize", 12,
        "rotate", 0,
        "formatter", "{value}"
        ));
        yAxis.put("axisLine", Map.of(
        "show", false,
        "lineStyle", Map.of(
        "color", "#cccccc",
        "width", 1,
        "style", "solid"
        )
        ));
        yAxis.put("splitLine", Map.of(
        "show", true,
        "lineStyle", Map.of(
        "color", "#858383",
        "width", 1,
        "style", "solid"
        )
        ));
        yAxis.put("axisValue", Map.of(
        "auto", true,
        "min", 10,
        "max", 100,
        "split", 10,
        "splitCount", 10
        ));
        yAxis.put("axisLabelFormatter", Map.of(
        "type", "auto",
        "unit", 1,
        "suffix", "",
        "decimalCount", 2,
        "thousandSeparator", true
        ));
        customStyle.put("yAxis", yAxis);

        // yAxisExt
        Map<String, Object> yAxisExt = new HashMap<>(yAxis);
        yAxisExt.put("position", "right");
        yAxisExt.put("splitLine", Map.of(
        "show", false,
        "lineStyle", Map.of(
        "color", "#858383",
        "width", 1,
        "style", "solid"
        )
        ));
        customStyle.put("yAxisExt", yAxisExt);

        // misc
        Map<String, Object> miscStyle = new HashMap<>();
        miscStyle.put("showName", false);
        miscStyle.put("color", "#FFFFFF");
        miscStyle.put("fontSize", 12);
        miscStyle.put("axisColor", "#999");
        miscStyle.put("splitNumber", 5);
        miscStyle.put("axisLine", Map.of(
        "show", true,
        "lineStyle", Map.of(
        "color", "#858383",
        "width", 1,
        "type", "solid"
        )
        ));
        miscStyle.put("axisTick", Map.of(
        "show", false,
        "length", 5,
        "lineStyle", Map.of(
        "color", "#FFFFFF",
        "width", 1,
        "type", "solid"
        )
        ));
        miscStyle.put("axisLabel", Map.of(
        "show", false,
        "rotate", 0,
        "margin", 8,
        "color", "#FFFFFF",
        "fontSize", "12",
        "formatter", "{value}"
        ));
        miscStyle.put("splitLine", Map.of(
        "show", true,
        "lineStyle", Map.of(
        "color", "#858383",
        "width", 1,
        "type", "solid"
        )
        ));
        miscStyle.put("splitArea", Map.of("show", true));
        miscStyle.put("axisValue", Map.of(
        "auto", true,
        "min", 10,
        "max", 100,
        "split", 10,
        "splitCount", 10
        ));
        customStyle.put("misc", miscStyle);

        template.put("customStyle", customStyle);

        // senior
        Map<String, Object> senior = new HashMap<>();
        senior.put("functionCfg", Map.of(
        "sliderShow", false,
        "sliderRange", Arrays.asList(0, 10),
        "sliderBg", "#FFFFFF",
        "sliderFillBg", "#BCD6F1",
        "sliderTextColor", "#999999",
        "emptyDataStrategy", "ignoreData",
        "emptyDataCustomValue", "",
        "emptyDataFieldCtrl", new ArrayList<>()
        ));
        senior.put("assistLineCfg", Map.of(
        "enable", false,
        "assistLine", new ArrayList<>()
        ));
        senior.put("threshold", Map.of(
        "enable", false,
        "gaugeThreshold", "",
        "liquidThreshold", "",
        "labelThreshold", new ArrayList<>(),
        "tableThreshold", new ArrayList<>(),
        "textLabelThreshold", new ArrayList<>()
        ));
        senior.put("scrollCfg", Map.of(
        "open", false,
        "row", 1,
        "interval", 2000,
        "step", 50
        ));
        senior.put("areaMapping", new HashMap<>());
        senior.put("bubbleCfg", Map.of(
        "enable", false,
        "speed", 1,
        "rings", 1,
        "type", "wave"
        ));
        template.put("senior", senior);

        // 其他参数
        template.put("isPlugin", false);
        template.put("dataFrom", "calc");
        template.put("chartExtRequest", Map.of(
        "user", 1,
        "filter", new ArrayList<>(),
        "drill", new ArrayList<>(),
        "resultCount", 1000,
        "resultMode", "all"
        ));
        template.put("calParams", new ArrayList<>());

        return template;
        }

    /**
     * 存储图表数据到Redis并返回缓存ID
     * @param data 图表数据
     * @return 包含缓存ID的字符串
     */
    private String storeChartDataToRedis(Object data) {
        String id = UUID.randomUUID().toString();
        String key = CHART_DATA_KEY_PREFIX + id;
        
        redisTemplate.opsForValue().set(key, data, CACHE_EXPIRATION_HOURS, TimeUnit.HOURS);
        
        return id;
    }

    public DatasetTreeResponse getDatasetTreeResponse() {
        try {
            // 创建HTTP请求头
            HttpHeaders headers = createAuthHeaders();

            // 创建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("busiFlag", "dataset");

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = BASE_URL + "/de2api/datasetTree/tree";
            ResponseEntity<DatasetTreeResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    request,
                    DatasetTreeResponse.class);

            // 返回响应数据
            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            // 创建一个错误响应
            DatasetTreeResponse errorResponse = new DatasetTreeResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("获取数据集列表失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取数据集详情
     * @param datasetId 数据集ID
     * @param needAllData 是否需要完整数据（true：返回完整数据，false：只返回字段元数据）
     * @return DatasetDetailResponse 对象
     */
    /**
     * 获取数据集详情
     * @param datasetId 数据集ID
     * @param needAllData 是否需要完整数据（true：返回完整数据，false：只返回字段元数据）
     * @return DatasetDetailResponse 对象
     */
    @Tool("获取特定数据集的详细信息，包括字段定义和数据预览")
    public DatasetDetailResponse getDatasetDetailResponse(String datasetId, boolean needAllData) {
        try {
            String url = BASE_URL + "/de2api/datasetTree/get/" + datasetId;
            HttpHeaders headers = createAuthHeaders();
            HttpEntity<String> entity = new HttpEntity<>("{}", headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();

                // 解析为对象
                DatasetDetailResponse detailResponse = objectMapper.readValue(responseBody, DatasetDetailResponse.class);
                return detailResponse;
            } else {
                // 处理错误情况
                DatasetDetailResponse errorResponse = new DatasetDetailResponse();
                errorResponse.setCode(response.getStatusCodeValue());
                errorResponse.setMsg("获取数据集详情失败: " + response.getStatusCode());
                return errorResponse;
            }
        } catch (Exception e) {
            // 处理异常
            DatasetDetailResponse errorResponse = new DatasetDetailResponse();
            errorResponse.setCode(500);
            errorResponse.setMsg("获取数据集详情异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 辅助方法：只有当源Map中的值不为null时，才添加到目标Map
    private void addIfNotNull(Map<String, Object> source, Map<String, Object> target, String key) {
        if (source.containsKey(key) && source.get(key) != null) {
            target.put(key, source.get(key));
        }
    }
}