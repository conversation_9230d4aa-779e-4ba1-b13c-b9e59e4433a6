<template>
  <div class="chart-container">
    <!-- 图表切换下拉选择器 -->
    <div v-if="showChartSwitcher" class="chart-switcher-dropdown">
      <div class="chart-dropdown-wrapper" @click="toggleDropdown" :class="{ 'open': dropdownOpen }">
        <!-- 当前选中的图表类型按钮 -->
        <div class="chart-current-btn">
          <div class="chart-icon-wrapper">
            <i :class="getCurrentChartIcon()"></i>
            <div v-if="switchingChart" class="switching-loader">
              <i class="el-icon-loading"></i>
            </div>
          </div>
          <span class="chart-type-text">图表切换</span>
          <i class="el-icon-arrow-down dropdown-arrow"></i>
        </div>

        <!-- 下拉选项 -->
        <div v-show="dropdownOpen" class="chart-dropdown-options">
          <div class="chart-type-grid">
            <button
              v-for="chartType in availableChartTypes"
              :key="chartType.type"
              :class="['chart-type-btn', {
                'active': currentChartType === chartType.type
              }]"
              :disabled="switchingChart"
              @click.stop="selectChartType(chartType.type)"
              :title="chartType.name"
            >
              <div class="chart-icon-wrapper">
                <i :class="chartType.icon"></i>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表显示区域 - 只在非switcherOnly模式下显示 -->
    <div v-if="!switcherOnly" class="chart-main">
      <div v-if="loading" class="chart-loading">
        <i class="el-icon-loading"></i>
        <span>加载图表中...</span>
      </div>
      <div v-else-if="error" class="chart-error">
        <i class="el-icon-warning"></i>
        <span>{{ errorMsg }}</span>
      </div>
      <div v-else class="chart-canvas-wrapper">
        <div ref="chartRef" class="chart-canvas resizable-chart"></div>
        <div class="resize-hint">拖拽右下角调整图表大小</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getData } from '../api/index';
import * as echarts from 'echarts';

export default {
  name: 'ChartDisplay',
  props: {
    chartConfig: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 推荐的图表类型列表
    recommendedChartTypes: {
      type: Array,
      default: () => []
    },
    // 是否显示图表切换器
    showChartSwitcher: {
      type: Boolean,
      default: false
    },
    // 是否只显示切换器（不显示图表）
    switcherOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      error: false,
      errorMsg: '',
      chartInstance: null,
      switchingChart: false,
      currentChartType: '',
      localChartConfig: null, // 本地图表配置副本
      dropdownOpen: false, // 下拉框是否打开
      resizeObserver: null, // 大小变化监听器

      // 支持的图表类型配置
      chartTypeConfig: {
        'bar': {
          name: '柱图',
          icon: 'chart-icon-bar',
          compatible: true
        },
        'line': {
          name: '线图',
          icon: 'chart-icon-line',
          compatible: true
        },
        'pie': {
          name: '饼图',
          icon: 'chart-icon-pie',
          compatible: true
        },
        'bar-horizontal': {
          name: '条形图',
          icon: 'chart-icon-bar-horizontal',
          compatible: true
        }
      }
    }
  },
  computed: {
    // 可用的图表类型列表
    availableChartTypes() {
      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];

      // 将当前类型放在第一位
      const currentType = this.currentChartType;
      const otherTypes = allTypes.filter(type => type !== currentType);
      const orderedTypes = currentType ? [currentType, ...otherTypes] : allTypes;

      return orderedTypes.map(type => ({
        type: type,
        name: this.chartTypeConfig[type]?.name || type,
        icon: this.chartTypeConfig[type]?.icon || 'chart-icon-bar',
        compatible: this.chartTypeConfig[type]?.compatible !== false
      }));
    }
  },
  watch: {
    chartConfig: {
      deep: true,
      handler() {
        // 重新渲染图表
        this.renderChart();
      }
    }
  },
  mounted() {
    // 初始化当前图表类型
    if (this.chartConfig && this.chartConfig.type) {
      this.currentChartType = this.chartConfig.type;
    }
    this.renderChart();
    // 添加窗口大小变化监听，以便图表能够自适应
    window.addEventListener('resize', this.resizeChart);
    // 添加点击外部关闭下拉框的监听
    document.addEventListener('click', this.handleClickOutside);
    // 设置图表容器大小变化监听
    this.setupResizeObserver();
  },
  beforeDestroy() {
    // 移除窗口大小变化监听，避免内存泄漏
    window.removeEventListener('resize', this.resizeChart);
    // 移除点击外部监听
    document.removeEventListener('click', this.handleClickOutside);
    // 移除大小变化监听器
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },
  methods: {
    // 切换图表类型
    async switchChartType(newType) {
      if (newType === this.currentChartType || this.switchingChart) {
        return;
      }

      console.log(`切换图表类型: ${this.currentChartType} -> ${newType}`);

      this.switchingChart = true;

      try {
        // 创建新的图表配置
        const newChartConfig = {
          ...this.chartConfig,
          type: newType
        };

        // 更新当前图表类型
        this.currentChartType = newType;
        this.localChartConfig = newChartConfig;

        // 触发事件通知父组件
        this.$emit('chart-type-changed', newChartConfig);

        // 重新渲染图表
        await this.$nextTick();
        this.renderChart();

      } catch (error) {
        console.error('切换图表类型失败:', error);
        this.$message.error('切换图表类型失败: ' + error.message);
      } finally {
        this.switchingChart = false;
      }
    },

    // 切换下拉框显示状态
    toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
    },

    // 选择图表类型并关闭下拉框
    selectChartType(chartType) {
      this.switchChartType(chartType);
      this.dropdownOpen = false;
    },

    // 获取当前图表类型的图标
    getCurrentChartIcon() {
      const currentType = this.availableChartTypes.find(type => type.type === this.currentChartType);
      return currentType ? currentType.icon : 'chart-icon-bar';
    },

    // 处理点击外部关闭下拉框
    handleClickOutside(event) {
      const dropdown = this.$el.querySelector('.chart-dropdown-wrapper');
      if (dropdown && !dropdown.contains(event.target)) {
        this.dropdownOpen = false;
      }
    },

    renderChart() {
      this.loading = true;
      this.error = false;

      // 如果没有配置或缺少必要的配置，显示错误
      if (!this.chartConfig || !this.chartConfig.type) {
        this.loading = false;
        this.error = true;
        this.errorMsg = '无效的图表配置';
        console.error('图表配置无效:', this.chartConfig);
        return;
      }

      console.log('开始渲染图表，配置:', this.chartConfig);

      // 检查chartConfig是否已包含完整数据
      if (this.chartConfig.data) {
        console.log('图表配置中已包含数据，直接使用');
        this.loading = false;

        try {
          // 将原始数据转换为echarts选项
          const options = this.convertToChartOptions(this.chartConfig);
          console.log('生成的echarts选项:', options);

          // 渲染图表
          this.drawChart(options);
        } catch (error) {
          console.error('处理图表数据失败:', error);
          this.error = true;
          this.errorMsg = '处理图表数据失败: ' + error.message;
        }
        return;
      }

      // 如果没有数据，才调用API获取
      getData(this.chartConfig).then(response => {
        console.log('图表数据API响应:', response);
        this.loading = false;

        if (!response) {
          this.error = true;
          this.errorMsg = '获取数据失败: 响应为空';
          console.error('图表数据响应为空');
          return;
        }

        if (response.code !== 0) {
          this.error = true;
          this.errorMsg = response.msg || '获取数据失败: ' + response.code;
          console.error('图表数据获取失败:', response);
          return;
        }

        const chartData = response.data || {};
        console.log('解析后的图表数据:', chartData);

        try {
          // 将原始数据转换为echarts选项
          const options = this.convertToChartOptions(chartData);
          console.log('生成的echarts选项:', options);

          // 渲染图表
          this.drawChart(options);
        } catch (error) {
          console.error('处理图表数据失败:', error);
          this.error = true;
          this.errorMsg = '处理图表数据失败: ' + error.message;
        }
      }).catch(error => {
        console.error('图表数据获取失败:', error);
        this.loading = false;
        this.error = true;
        this.errorMsg = '图表数据获取失败: ' + error.message;
      });
    },

    // 将原始数据转换为echarts选项
    convertToChartOptions(chartData) {
      console.log('转换图表数据为echarts选项，数据:', chartData);

      // 使用配置中的类型
      const type = this.chartConfig.type;

      // 检查是否已经是完整的数据格式
      if (chartData.data && Array.isArray(chartData.data)) {
        console.log('检测到标准数据格式');
        // 根据图表类型调用不同的处理函数
        switch(type) {
          case 'bar':
            return this.getBarChartOptions(chartData);
          case 'line':
            return this.getLineChartOptions(chartData);
          case 'pie':
            return this.getPieChartOptions(chartData);
          case 'bar-horizontal':
            return this.getBarHorizontalOptions(chartData);
          default:
            return this.getDefaultOptions();
        }
      } else {
        console.log('检测到非标准数据格式，尝试提取数据');
        
        // 尝试从复杂对象中提取数据
        let extractedData = null;
        
        // 检查是否有data.data字段（嵌套数据结构）
        if (chartData.data && chartData.data.data) {
          console.log('从嵌套结构中提取数据');
          extractedData = {
            type: chartData.data.type || this.chartConfig.type,
            data: chartData.data.data,
            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []
          };
        }
        
        // 如果没有提取到数据，使用默认选项
        if (!extractedData) {
          console.log('无法提取数据，使用默认选项');
          return this.getDefaultOptions();
        }
        
        console.log('提取的数据:', extractedData);
        
        // 根据提取的数据类型调用不同的处理函数
        switch(extractedData.type) {
          case 'bar':
            return this.getBarChartOptions(extractedData);
          case 'line':
            return this.getLineChartOptions(extractedData);
          case 'pie':
            return this.getPieChartOptions(extractedData);
          case 'bar-horizontal':
            return this.getBarHorizontalOptions(extractedData);
          default:
            return this.getDefaultOptions();
        }
      }
    },
    
    // 柱状图选项生成
    getBarChartOptions(chartData) {
      console.log('生成柱状图选项，数据:', chartData);
      
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      console.log('处理后的数据:', data);
      console.log('指标:', metrics);
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 折线图选项生成
    getLineChartOptions(chartData) {
      const { data = [], metrics = [] } = chartData;
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'line',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'line',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: this.chartConfig.title || '',
          top: 20,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        grid: {
          top: 80,
          left: 60,
          right: 40,
          bottom: 60,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: series.map(s => s.name),
          top: 50
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 饼图选项生成
    getPieChartOptions(chartData) {
      const { data = [] } = chartData;
      
      const seriesData = data.map(item => ({
        name: item.field,
        value: item.value
      }));
      
      return {
        title: {
          text: this.chartConfig.title || '',
          top: 20,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 80,
          data: seriesData.map(item => item.name)
        },
        series: [{
          name: '数据',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: seriesData
        }]
      };
    },
    
    // 条形图选项生成（水平柱状图）
    getBarHorizontalOptions(chartData) {
      console.log('生成条形图选项，数据:', chartData);
      
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      console.log('处理后的数据:', data);
      console.log('指标:', metrics);
      
      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴
      const yAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',  // 使用标准的'bar'类型
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',  // 使用标准的'bar'类型
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        // 关键区别：交换X轴和Y轴的配置
        xAxis: {
          type: 'value'  // 值轴在X轴
        },
        yAxis: {
          type: 'category',  // 类别轴在Y轴
          data: yAxisData
        },
        series: series
      };
    },
    
    // 获取默认图表选项
    getDefaultOptions() {
      console.log('使用默认图表选项');
      return {
        title: {
          text: this.chartConfig.title || '数据图表'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['暂无数据']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: this.chartConfig.type || 'bar',
          data: [0]
        }]
      };
    },
    
    // 绘制图表
    drawChart(options) {
      try {
        console.log('开始绘制图表');
        if (!this.$refs.chartRef) {
          console.error('图表DOM引用不存在');
          this.error = true;
          this.errorMsg = '图表渲染失败: DOM不存在';
          return;
        }

        // 如果已有实例，先销毁
        if (this.chartInstance) {
          this.chartInstance.dispose();
        }

        // 创建新的图表实例
        this.chartInstance = echarts.init(this.$refs.chartRef);
        this.chartInstance.setOption(options);
        console.log('图表绘制完成');
      } catch (error) {
        console.error('图表绘制失败:', error);
        this.error = true;
        this.errorMsg = '图表渲染失败: ' + error.message;
      }
    },
    
    // 调整图表大小
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },

    // 设置图表容器大小变化监听
    setupResizeObserver() {
      this.$nextTick(() => {
        const chartContainer = this.$refs.chartRef;
        if (chartContainer && window.ResizeObserver) {
          this.resizeObserver = new ResizeObserver(() => {
            // 当容器大小改变时，重新调整图表大小
            this.$nextTick(() => {
              this.resizeChart();
            });
          });
          this.resizeObserver.observe(chartContainer);
        }
      });
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: auto;
  min-height: 280px; /* 减少高度从350px到280px */
  position: relative;
  max-width: 168px; /* 缩短40%：从280px到168px */
  margin: 0 auto;
  z-index: 1;
  box-sizing: border-box;
  isolation: isolate; /* 创建新的层叠上下文 */
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: 280px; /* 减少高度从350px到280px */
}

/* 图表容器包装器 */
.chart-canvas-wrapper {
  position: relative;
  /* 不设置固定宽高，让子元素自由调整大小 */
  display: inline-block;
  min-width: 300px;
  min-height: 200px;
}

/* 可调整大小的图表容器 */
.chart-canvas {
  /* 设置适合对话内容的初始尺寸，用户可以拖拽调整 */
  width: 600px;
  height: 350px;
  position: relative;
  z-index: 1;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 添加拖拽调整大小功能 */
.resizable-chart {
  resize: both;
  overflow: hidden;
  min-width: 300px;
  min-height: 200px;
  /* 移除 max-width 限制，允许用户往右拖拽到任意宽度 */
  max-height: 800px;
  /* 确保拖拽功能正常工作的额外属性 */
  display: block;
  box-sizing: border-box;
}

/* 鼠标悬停时显示可调整大小的提示 */
.resizable-chart:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
}

/* 调整大小提示文字 */
.resize-hint {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 11px;
  color: #909399;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.chart-canvas-wrapper:hover .resize-hint {
  opacity: 1;
}

.chart-loading, .chart-error, .chart-switch-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  background: rgba(255,255,255,0.9);
}

.chart-loading i, .chart-error i, .chart-switch-loading i {
  font-size: 24px;
  margin-bottom: 10px;
}

.chart-error {
  color: #F56C6C;
}

.chart-switch-loading {
  background: rgba(255,255,255,0.8);
  backdrop-filter: blur(2px);
}

/* 图表切换过渡动画 */
.chart-fade-enter-active, .chart-fade-leave-active {
  transition: opacity 0.3s ease;
}

.chart-fade-enter, .chart-fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
/* 图表切换下拉选择器样式 */
.chart-switcher-dropdown {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.chart-dropdown-wrapper {
  position: relative;
  cursor: pointer;
}

.chart-current-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 28px;
  padding: 0 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  color: #606266;
  font-size: 12px;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-width: 100px;
}

.chart-current-btn:hover {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.chart-dropdown-wrapper.open .chart-current-btn {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.chart-type-text {
  font-size: 12px;
  color: inherit;
}

.dropdown-arrow {
  font-size: 10px;
  color: inherit;
  transition: transform 0.3s ease;
}

.chart-dropdown-wrapper.open .dropdown-arrow {
  transform: rotate(180deg);
}

.chart-dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  padding: 8px;
  min-width: 120px;
}

.chart-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.chart-type-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-type-btn:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.chart-type-btn.active {
  border-color: #409eff;
  background: #409eff;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.chart-type-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.chart-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.switching-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.switching-loader i {
  font-size: 12px;
  animation: rotating 1s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 自定义图表图标样式 */
.chart-icon-bar::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 17h2v-7H7v7zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-line::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-pie::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-bar-horizontal::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z' transform='rotate(90 12 12)'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

@media (max-width: 768px) {
  .chart-container {
    max-width: 100%;
    margin: 0;
    padding: 0 10px;
  }

  .chart-wrapper {
    height: 250px; /* 移动端进一步减少高度 */
  }
}
</style>